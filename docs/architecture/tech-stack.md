# Tech Stack

## Technology Stack Table
| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| Frontend Language | TypeScript | ~5.4.5 | Type safety, modern JS features | Industry standard for robust frontend development, reduces errors. |
| Frontend Framework | Next.js | ~13.5 | Fullstack React framework with SSR, SSG | A stable, mature, and widely-used version for production applications. |
| UI Component Library | shadcn/ui | latest | Foundational, unstyled accessible components | Provides full ownership of component code, highly customizable. |
| State Management | TanStack Query | ~5.51.1 | Server state management, caching, refetching | Simplifies data fetching, caching, and synchronization. |
| Backend Language | TypeScript | ~5.4.5 | Type safety, code sharing with frontend | Consistency across the stack, improved maintainability. |
| Backend Framework | Node.js / Express | ~4.19.2 | Lightweight, flexible backend framework | Mature, large ecosystem, easy to build REST APIs. |
| API Style | REST | - | Standard for client-server communication | Well-understood, broad support, good for CRUD tasks. |
| Database | Supabase (PostgreSQL) | latest | Open source Firebase alternative, relational DB | Provides auth, storage, and a powerful Postgres DB. |
| Cache | Redis | latest | In-memory data store for caching sessions, etc. | High performance, versatile for various caching needs. |
| File Storage | Supabase Storage | latest | S3-compatible object storage | Integrated with Supabase, easy to manage files. |
| Authentication | Supabase Auth | latest | Handles user auth, JWTs, social logins | Secure, easy to integrate, feature-rich. |
| Frontend Testing | Jest, React Testing Library | latest | Unit and component testing | Standard for React, focuses on user behavior. |
| Backend Testing | Jest, Supertest | latest | Unit and API endpoint testing | Fast, easy to mock, good for testing Express APIs. |
| E2E Testing | Playwright | latest | End-to-end browser automation | Reliable, fast, supports multiple browsers. |
| Build Tool / Bundler | Webpack | latest | Module bundler for JavaScript | Default, stable, and powerful bundler integrated with Next.js 13. |
| IaC Tool | Terraform | latest | Infrastructure as Code for cloud resources | Declarative, multi-platform, manages infrastructure state. |
| CI/CD | GitHub Actions | - | Automated build, test, and deployment | Natively integrated with GitHub, highly configurable. |
| Monitoring | Vercel Analytics, Sentry | latest | Performance monitoring and error tracking | Easy to set up, provides valuable insights. |
| Logging | Netlify Logs, Sentry | latest | Centralized logging for backend and frontend | Essential for debugging and monitoring application health. |
| CSS Framework | Tailwind CSS | latest | Utility-first CSS framework | Rapid styling, highly customizable, no unused CSS. |

---
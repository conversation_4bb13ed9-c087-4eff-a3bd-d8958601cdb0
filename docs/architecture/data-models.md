# Data Models

### 1. User (Ngư<PERSON>i dùng)
*   **<PERSON><PERSON><PERSON> đích:** Đ<PERSON><PERSON> diện cho một khách hàng đã đăng ký tài khoản trên hệ thống.
*   **TypeScript Interface:**
    ```typescript
    interface User {
      id: string;
      email: string;
      full_name?: string;
      avatar_url?: string;
      created_at: string;
    }
    ```
*   **<PERSON><PERSON><PERSON> quan hệ:** Một `User` có thể có nhiều `Order` và `Review`.

### 2. Product (Sản phẩm)
*   **Mục đích:** Đại diện cho một mặt hàng giày được bán trên trang web.
*   **TypeScript Interface:**
    ```typescript
    interface Product {
      id: string;
      name: string;
      description: string;
      price: number;
      image_urls: string[];
      brand: string;
      category: string;
      inventory_count: number;
      created_at: string;
    }
    ```
*   **<PERSON><PERSON><PERSON> quan hệ:** <PERSON><PERSON>t `Product` có thể có nhiều `Review` và nằm trong nhiều `OrderItem`.

### 3. Order (<PERSON><PERSON><PERSON> hàng)
*   **M<PERSON><PERSON> đích:** Đ<PERSON><PERSON> diện cho một giao dịch mua hàng đã được hoàn tất.
*   **TypeScript Interface:**
    ```typescript
    interface Order {
      id: string;
      user_id: string;
      total_amount: number;
      status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
      shipping_address: {
        recipient_name: string;
        phone: string;
        address: string;
        city: string;
      };
      payment_method: string;
      created_at: string;
      order_items?: OrderItem[];
    }
    ```
*   **Mối quan hệ:** Một `Order` thuộc về một `User` và có nhiều `OrderItem`.

### 4. OrderItem (Mục trong đơn hàng)
*   **Mục đích:** Một dòng sản phẩm cụ thể trong một đơn hàng.
*   **TypeScript Interface:**
    ```typescript
    interface OrderItem {
      id: string;
      order_id: string;
      product_id: string;
      quantity: number;
      price_at_purchase: number;
    }
    ```
*   **Mối quan hệ:** Thuộc về một `Order` và liên quan đến một `Product`.

### 5. Review (Đánh giá)
*   **Mục đích:** Lưu trữ đánh giá của người dùng về một sản phẩm.
*   **TypeScript Interface:**
    ```typescript
    interface Review {
      id: string;
      user_id: string;
      product_id: string;
      rating: number;
      comment?: string;
      created_at: string;
      user?: User;
    }
    ```
*   **Mối quan hệ:** Thuộc về một `User` và một `Product`.

---
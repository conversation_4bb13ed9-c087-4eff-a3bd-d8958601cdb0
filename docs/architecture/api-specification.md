# API Specification

## REST API Specification (OpenAPI 3.0)
```yaml
openapi: 3.0.1
info:
  title: "Shoe Store API"
  description: "API chính thức cho nền tảng thương mại điện tử giày."
  version: "1.0.0"
servers:
  - url: "/api"
    description: "Server API chính"
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
paths:
  /products:
    get:
      summary: "Lấy danh sách sản phẩm"
      # ...
  /products/{productId}:
    get:
      summary: "Lấy chi tiết một sản phẩm"
      # ...
  /orders:
    post:
      summary: "Tạo một đơn hàng mới"
      # ...
```

---
# Phát triển nền tảng thương mại điện tử giày Fullstack Architecture Document

## Table of Contents

- [Phát triển nền tảng thương mại điện tử giày Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
      - [1. User (Người dùng)](./data-models.md#1-user-ngi-dng)
      - [2. Product (Sản phẩm)](./data-models.md#2-product-sn-phm)
      - [3. Order (Đơn hàng)](./data-models.md#3-order-n-hng)
      - [4. OrderItem (Mục trong đơn hàng)](./data-models.md#4-orderitem-mc-trong-n-hng)
      - [5. Review (Đánh giá)](./data-models.md#5-review-nh-gi)
  - [API Specification](./api-specification.md)
    - [REST API Specification (OpenAPI 3.0)](./api-specification.md#rest-api-specification-openapi-30)
  - [Components](./components.md)
      - [1. Frontend Application](./components.md#1-frontend-application)
      - [2. API Service](./components.md#2-api-service)
      - [3. Auth Service](./components.md#3-auth-service)
      - [4. Database Service](./components.md#4-database-service)
      - [5. Storage Service](./components.md#5-storage-service)
      - [6. Payment Service](./components.md#6-payment-service)
  - [External APIs](./external-apis.md)
      - [1. Payment Gateway (Ví dụ: Stripe)](./external-apis.md#1-payment-gateway-v-d-stripe)
      - [2. Shipping Provider (Ví dụ: Giao Hàng Nhanh)](./external-apis.md#2-shipping-provider-v-d-giao-hng-nhanh)
  - [Core Workflows](./core-workflows.md)
      - [User Checkout Sequence Diagram](./core-workflows.md#user-checkout-sequence-diagram)
  - [Database Schema](./database-schema.md)
  - [Frontend Architecture](./frontend-architecture.md)
      - [Component Organization](./frontend-architecture.md#component-organization)
      - [State Management](./frontend-architecture.md#state-management)
  - [Backend Architecture](./backend-architecture.md)
      - [Service Architecture](./backend-architecture.md#service-architecture)
      - [Data Access Layer](./backend-architecture.md#data-access-layer)
      - [Authentication](./backend-architecture.md#authentication)
  - [Unified Project Structure](./unified-project-structure.md)
  - [Development Workflow](./development-workflow.md)
  - [Deployment Architecture](./deployment-architecture.md)
  - [Security and Performance](./security-and-performance.md)
  - [Testing Strategy](./testing-strategy.md)
  - [Coding Standards](./coding-standards.md)
  - [Error Handling Strategy](./error-handling-strategy.md)
  - [Monitoring and Observability](./monitoring-and-observability.md)

# Core Workflows

### User Checkout Sequence Diagram
```mermaid
sequenceDiagram
    actor User
    participant Frontend as Frontend App
    participant Backend as API Service
    participant Stripe as Payment Service
    participant DB as Database Service

    User->>Frontend: <PERSON><PERSON><PERSON> n<PERSON><PERSON> "Thanh toán"
    Frontend->>Backend: POST /api/orders/initiate
    activate Backend
    Backend->>Stripe: POST /v1/payment_intents
    activate Stripe
    Stripe-->>Backend: Tr<PERSON> về client_secret
    deactivate Stripe
    Backend->>DB: <PERSON><PERSON><PERSON> đơn hàng "pending"
    Backend-->>Frontend: Tr<PERSON> về client_secret
    deactivate Backend
    User->>Stripe: Nhậ<PERSON> thông tin thẻ
    Stripe-->>User: <PERSON><PERSON><PERSON> thị kết quả
    Stripe->>Backend: POST /api/webhooks/stripe
    activate Backend
    Backend->>DB: Cập nhật trạng thái đơn hàng
    deactivate Backend
```

---
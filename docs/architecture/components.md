# Components

### 1. Frontend Application
*   **Trách nhiệm:** <PERSON><PERSON><PERSON> thị <PERSON>, xử lý tương tác người dùng, giao tiếp với API Service.
*   **Ng<PERSON>n xếp công nghệ:** Next.js, React, TypeScript, TanStack Query, shadcn/ui.

### 2. API Service
*   **Trách nhiệm:** Xử lý logic nghiệp vụ, nhận request từ Frontend, giao tiếp với các dịch vụ backend khác.
*   **Ngăn xếp công nghệ:** Node.js, Express, TypeScript (trên Netlify Functions).

### 3. Auth Service
*   **Trách nhiệm:** Quản lý vòng đời người dùng (đăng ký, đăng nhập...).
*   **Ngăn xếp công nghệ:** Supabase Auth.

### 4. Database Service
*   **Trách nhiệm:** <PERSON><PERSON><PERSON> trữ và truy xu<PERSON>t dữ liệu có cấu trúc.
*   **Ngăn xếp công nghệ:** Supabase (PostgreSQL).

### 5. Storage Service
*   **Trách nhiệm:** Lưu trữ file media.
*   **Ngăn xếp công nghệ:** Supabase Storage.

### 6. Payment Service
*   **Trách nhiệm:** Xử lý giao dịch thanh toán an toàn.
*   **Ngăn xếp công nghệ:** API của bên thứ ba (ví dụ: Stripe).

---
# Database Schema
```sql
-- <PERSON><PERSON><PERSON> lưu thông tin công khai của người dùng
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  avatar_url TEXT
);

-- Bảng sản phẩm
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  price NUMERIC(10, 2) NOT NULL,
  -- ...
);

-- ... c<PERSON>c bảng <PERSON>h<PERSON>
```

---
---
# NOTE: Prompt dành cho công cụ sinh UI (v0, Lovable, v.v.)
# <PERSON><PERSON><PERSON> tiêu: <PERSON>h ra frontend hiện đại, c<PERSON> thể build ngay, bám sát đặc tả trong docs/front-end-spec.md
# Công nghệ đề xuất: Next.js (App Router), TypeScript, Tailwind CSS, shadcn/ui
# Y<PERSON>u cầu bắt buộc: Accessible by default (WCAG AA), Mobile-first, hi<PERSON><PERSON> năng tốt, cấu trúc thư mục rõ ràng
# Lưu ý: Chỉ sinh frontend (mock API/data). Không commit secrets.
---

# Project: E-commerce giày – UI Generation Prompt

## 1) Context & Personas
- Người mua sắm tiện lợi: tối giản thao tác, rõ ràng CTA.
- Người tìm kiếm giá trị: bộ lọ<PERSON>, s<PERSON><PERSON> xế<PERSON>, hiển thị giá/khuyến mại rõ ràng.
- Người yêu thời trang: <PERSON><PERSON><PERSON>nh lớn, thẻ sản phẩm đẹp, bộ sưu tập.

## 2) <PERSON><PERSON><PERSON><PERSON> kế tổng thể (theo `docs/front-end-spec.md`)
- Nguyên tắc: Clarity > Cleverness, Progressive Disclosure, Consistency, Immediate Feedback, Accessible by Default, Mobile-first.
- IA/Sitemap chính (tham khảo, có thể tinh gọn hợp lý):
  - Trang chủ
  - Danh mục sản phẩm (collections: Giày thể thao, Cao gót, …)
  - Tìm kiếm + trang kết quả
  - Tài khoản: Đăng nhập/Đăng ký, Hồ sơ, Lịch sử đơn hàng (chỉ UI)
  - Giỏ hàng, Thanh toán, Xác nhận đơn hàng, Thành công

## 3) Stack & Guidelines
- Next.js App Router + TypeScript
- Tailwind CSS (config chuẩn), shadcn/ui
- ESLint + Prettier cấu hình chuẩn
- i18n có sẵn hook chuẩn (placeholder), text tách file để dễ dịch
- Tối ưu ảnh (next/image), lazy loading phù hợp
- Tương thích dark mode (prefers-color-scheme)

## 4) Pages bắt buộc
- `/` – Trang chủ: hero, bộ sưu tập nổi bật, lưới sản phẩm mới/khuyến mãi
- `/category/[slug]` – Danh mục: sidebar bộ lọc (loại, size, giá), sort (price asc/desc, newest)
- `/search` – Kết quả tìm kiếm: thanh search prominent, filters inline, empty state
- `/product/[slug]` – Chi tiết sản phẩm: gallery ảnh, chọn size/màu, giá/discount, đánh giá (giả lập), add-to-cart
- `/cart` – Giỏ hàng: list items, update qty, remove, subtotal, CTA checkout
- `/checkout` – Thanh toán (giả lập): thông tin giao hàng, phương thức thanh toán (UI), validate form
- `/order/confirmation` – Xác nhận: tóm tắt đơn hàng (mock)
- `/auth/login`, `/auth/register` – Form auth (chỉ UI + validate client)
- `/account` – Hồ sơ (đọc-only mock), `/orders` – Lịch sử đơn hàng (mock list)

## 5) Components cốt lõi
- Button (primary/secondary/outline/ghost/link; sm/md/lg; icon-left/right; states)
- Input, Select, Checkbox, Radio, Textarea, FormField với message lỗi
- ProductCard (ảnh, tên, giá, badge khuyến mãi), ProductGrid responsive
- Header/Nav: logo, main nav, search, cart indicator, account menu; sticky, mobile menu
- Footer với links, subscription form (mock)
- Modal/Dialog chuẩn (alert/confirm/form)
- Toast/Inline feedback cho hành động (add to cart, form submitted)

## 6) User Flows chính (ràng buộc UI)
- Flow Tìm kiếm & Mua hàng: từ `/` hoặc search → `/search` → `/product/[slug]` → add-to-cart → `/cart` → `/checkout` → xác nhận
- Flow Duyệt danh mục: `/category/[slug]` → filter/sort → `/product/[slug]` → add-to-cart …
- Mỗi bước phải có feedback rõ ràng (loading, success, error UI giả lập)

## 7) Accessibility (bắt buộc)
- Điều hướng bằng bàn phím đầy đủ; focus ring rõ ràng
- Contrast đạt tối thiểu AA
- Labels/aria-* đầy đủ cho form, icon button có aria-label
- Trap focus trong Modal, đóng bằng Esc và overlay

## 8) Performance targets
- TTFB và LCP hợp lý cho app demo: tối ưu critical UI, ưu tiên lazy-load phần dưới fold
- Ảnh dùng `next/image` với kích thước responsive; tránh layout shift

## 9) Data & State (mock)
- Không gọi API thật. Tạo `lib/mock/`:
  - `products.ts`: danh sách sản phẩm mẫu (id, slug, name, images, price, discount, rating, sizes, colors, category)
  - `categories.ts`, `orders.ts` (mẫu)
- Cart state bằng zustand hoặc context đơn giản; persist localStorage
- Tạo helpers format tiền tệ, rating, price with discount

## 10) Cấu trúc thư mục kỳ vọng
```
app/
  (marketing)/
  (shop)/
    category/[slug]/page.tsx
    product/[slug]/page.tsx
    cart/page.tsx
    checkout/page.tsx
    order/confirmation/page.tsx
  (account)/
    auth/login/page.tsx
    auth/register/page.tsx
    account/page.tsx
    orders/page.tsx
  page.tsx
components/
  ui/ (button, input, dialog, toast … – shadcn/ui + tuỳ biến)
  product/
    product-card.tsx
    product-grid.tsx
  layout/
    header.tsx
    footer.tsx
lib/
  mock/
    products.ts
    categories.ts
    orders.ts
  utils/
    currency.ts
    rating.ts
store/
  cart.ts
styles/
  globals.css
```

## 11) Design system
- Tailwind config màu/phông rõ ràng; tokens cho spacing, radius, shadow
- Variants cho Button, Inputs thống nhất
- Card product có variants (withShadow/withBorder), states (hover/selected)

## 12) Constraints & Acceptance Criteria
- Build chạy `pnpm install && pnpm dev` (hoặc `npm run dev`) ngay sau sinh
- Không có lỗi ESLint/TypeScript cơ bản
- Mobile-first; tối thiểu màn hình 360px; desktop >=1280px hiển thị lưới 4-5 cột sản phẩm
- Demo được 2 flow chính end-to-end với mock data

## 13) Nice-to-have (tuỳ chọn)
- Breadcrumbs cho category/product
- Skeleton loading cho lưới sản phẩm & PDP
- Animations nhẹ (framer-motion) nhưng tôn trọng perf & a11y

## 14) Handover
- Gắn nhãn file sinh ra theo cấu trúc trên, đảm bảo import đường dẫn tương đối đúng
- Xuất hướng dẫn ngắn trong README.md: cách chạy, cấu hình môi trường (nếu cần), nơi sửa mock data

## 15) Gợi ý prompt kỹ thuật cho công cụ sinh
"""
Bạn là công cụ sinh UI. Hãy tạo một frontend Next.js (App Router, TS) dùng Tailwind + shadcn/ui theo các yêu cầu trên. Tập trung vào tính khả dụng, accessibility, performance, mobile-first, và cấu trúc rõ ràng. Tạo sẵn components và mock data cần thiết để demo hai luồng mua hàng chính.
"""

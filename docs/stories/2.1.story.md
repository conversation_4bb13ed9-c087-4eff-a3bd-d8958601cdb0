---
docType: story
epic: 2
story: 1
title: <PERSON><PERSON><PERSON> dựng Trang chủ và các thành phần cơ bản
status: Draft
---

# Story 2.1: <PERSON><PERSON><PERSON> dựng Trang chủ và các thành phần cơ bản

**As a** user, **I want to** see an engaging home page with featured products and promotions, **so that I can** quickly discover interesting items.

## Acceptance Criteria

- **2.1.1:** Trang chủ (`/`) hiển thị một `HeroBanner` nổi bật ở đầu trang.
- **2.1.2:** Trang chủ hiển thị một khu vực (ví dụ: `ProductCarousel` hoặc `ProductGrid`) cho các "Sản phẩm Nổi bật/Bán chạy".
- **2.1.3:** Trang chủ hiển thị một khu vực cho "Thương hiệu nổi bật".
- **2.1.4:** Trang chủ hiển thị một `PromoBanner` cho các ưu đãi đặc biệt.
- **2.1.5:** Trang chủ hiển thị một khu vực `Testimonials` để hiển thị đánh giá từ khách hàng.

## Dev Notes

### Previous Story Insights

- The backend API for fetching products (`GET /api/products`) was created in Story 1.3 and is ready for consumption.
- The CI/CD pipeline is set up, so changes pushed to the `main` branch will be deployed automatically.
- This story is purely frontend-focused, building upon the Next.js application in `apps/web`.

### Data Models

- The components in this story will consume `Product` data returned from the API. The `Product` type is available in `packages/shared-types`.
- A `Testimonial` type will be needed for the testimonials section. A simple interface can be created for now with mock data.
    ```typescript
    interface Testimonial {
      quote: string;
      author: string;
      location?: string;
    }
    ```
- [Source: architecture/data-models.md]

### API Specifications

- The "Featured Products" section should fetch data from the `GET /api/products` endpoint.
- For now, it can fetch all products. Logic for determining "featured" or "best-selling" will be added in a future story.
- Data fetching should be implemented using TanStack Query for caching and server state management. [Source: architecture/tech-stack.md]

### Component Specifications

- **`HeroBanner` (AC: 2.1.1)**: A large, visually appealing component with a headline, a short description, and a primary Call-to-Action (CTA) button (e.g., "Shop Now").
- **`ProductCarousel` / `ProductGrid` (AC: 2.1.2)**: A component that takes a list of products and displays them in a carousel or grid format. This component will use the `ProductCard` component for individual items.
- **`ProductCard`**: A reusable card to display a single product's image, name, and price. This should be a core component in `packages/ui`.
- **`BrandShowcase` (AC: 2.1.3)**: A simple component that displays a list of brand logos.
- **`PromoBanner` (AC: 2.1.4)**: A full-width banner component for promotional messages.
- **`Testimonials` (AC: 2.1.5)**: A section, possibly a slider, to display quotes from customers.
- All components should be built using the shadcn/ui library and follow the project's styling guidelines. They should be responsive and accessible. [Source: architecture/frontend-architecture.md, architecture/components.md]

### File Locations

- **Home Page**: `apps/web/src/app/page.tsx`
- **Reusable Components** (e.g., `ProductCard`): `packages/ui/src/components/`
- **Home Page Specific Components** (e.g., `HeroBanner`, `Testimonials`): `apps/web/src/app/_components/` (or a more specific `home/` subdirectory).
- **Data Fetching Logic**: Hooks for TanStack Query can be placed in `apps/web/src/lib/hooks/`.

### Testing Requirements

- Write component tests with Vitest/RTL for each new component (`HeroBanner`, `ProductCard`, `Testimonials`, etc.).
- Tests should verify that components render correctly given mock props.
- Mock the API call for `GET /api/products` when testing the `ProductCarousel`/`ProductGrid` component.
- [Source: architecture/testing-strategy.md]

## Tasks / Subtasks

1.  **Create Reusable `ProductCard` Component**
    -   [ ] Create the `ProductCard` component in `packages/ui`.
    -   [ ] It should accept a `product` object as a prop and display its image, name, and price.
    -   [ ] Write component tests for `ProductCard`.

2.  **Implement Data Fetching for Products**
    -   [ ] Create a custom hook (e.g., `useProducts`) that uses TanStack Query to fetch data from the `GET /api/products` endpoint.
    -   [ ] Ensure proper loading and error states are handled.

3.  **Build Home Page Components (AC: 2.1.1, 2.1.2, 2.1.3, 2.1.4, 2.1.5)**
    -   [ ] Create the `HeroBanner` component.
    -   [ ] Create the `ProductCarousel` (or `ProductGrid`) component that uses the `useProducts` hook and maps the data to `ProductCard` components.
    -   [ ] Create the `BrandShowcase` component (can use static mock data for now).
    -   [ ] Create the `PromoBanner` component.
    -   [ ] Create the `Testimonials` component (can use static mock data for now).

4.  **Assemble the Home Page**
    -   [ ] In `apps/web/src/app/page.tsx`, import and arrange the components created in the previous step to build the full home page layout.
    -   [ ] Ensure the page is responsive across mobile and desktop breakpoints.

5.  **Write Tests for Home Page**
    -   [ ] Write component tests for all new components created.
    -   [ ] Write a simple integration test for the home page itself to ensure all sections are rendered.

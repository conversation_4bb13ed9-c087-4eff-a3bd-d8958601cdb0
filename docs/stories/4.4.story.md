---
docType: story
epic: 4
story: 4
title: "[Technical Story] Rà soát và Tối ưu hóa Hiệu suất & Core Web Vitals"
status: Draft
---

# Story 4.4: [Technical Story] Rà soát và Tối ưu hóa Hiệu suất & Core Web Vitals

**As a** developer, **I want to** audit and refactor the application to ensure it adheres to performance best practices, **so that** the user experience is fast, smooth, and achieves a high score on Core Web Vitals.

**Note:** This is a technical story. It does not introduce new user-facing features but focuses on improving the quality and performance of the existing codebase. It should be executed after the main features of Epics 1-3 are implemented.

## Acceptance Criteria

- **4.4.1:** Tất cả các hình ảnh trên toàn bộ trang web được render bằng component `<Image>` của Next.js với các thuộc tính `width`, `height`, và `priority` được cấu hình hợp lý.
- **4.4.2:** Tất cả các font chữ được tải và tối ưu hóa thông qua `next/font`.
- **4.4.3:** Bundle size của ứng dụng được phân tích. Bất kỳ thư viện lớn hoặc không cần thiết nào được xác định và xem xét để loại bỏ hoặc thay thế.
- **4.4.4:** Các component lớn không cần thiết cho lần render đầu tiên được xác định và chuyển đổi sang tải lười (lazy loading) bằng `next/dynamic` hoặc được cấu trúc lại thành Server Components.
- **4.4.5:** Điểm Lighthouse/PageSpeed Insights cho các trang chính (Trang chủ, Trang danh mục, Trang chi tiết sản phẩm) đạt tối thiểu 90 điểm cho hạng mục "Performance".

## Dev Notes

### Technical Approach

- This story is an audit. The developer will need to systematically review the existing codebase (`apps/web`) and identify areas that do not follow the performance guidelines laid out in the architecture.
- The primary tools for this task are built into Next.js (`next/image`, `next/font`, `next/dynamic`) and external tools like Google's PageSpeed Insights and `@next/bundle-analyzer`.

### Key Areas for Audit

- **Image Optimization (AC 4.4.1)**: Search the codebase for any standard `<img>` tags and replace them with `<Image>`. Ensure `priority` prop is used for images above the fold (e.g., the Hero Banner).
- **Font Optimization (AC 4.4.2)**: Check the main layout file to ensure fonts are loaded via `next/font`. Remove any `<link>` tags for fonts from the document `<head>`.
- **Bundle Size Analysis (AC 4.4.3)**:
    - Install and configure `@next/bundle-analyzer`.
    - Run the analyzer and inspect the output to identify large dependencies.
    - Look for opportunities to reduce size, such as replacing heavy libraries (e.g., moment.js) with lighter alternatives (e.g., date-fns).
- **Code Splitting (AC 4.4.4)**:
    - Identify large, client-side components that are not immediately visible (e.g., modals, complex components below the fold).
    - Use `next/dynamic` to import these components lazily.
    - Leverage React Server Components where possible to move rendering work to the server.
- **Performance Measurement (AC 4.4.5)**:
    - Use the production build (`pnpm build` then `pnpm start`) for testing.
    - Run Google PageSpeed Insights on the deployed preview/production URLs for the main pages.
    - Analyze the report for specific recommendations (e.g., "Reduce initial server response time", "Eliminate render-blocking resources").

## Tasks / Subtasks

1.  **Setup Bundle Analyzer**
    -   [ ] Install `@next/bundle-analyzer`.
    -   [ ] Configure `next.config.js` to enable the analyzer when a specific environment variable is set (e.g., `ANALYZE=true`).

2.  **Audit and Refactor Image Components (AC 4.4.1)**
    -   [ ] Scan the `apps/web` codebase for `<img>` tags.
    -   [ ] Replace them with the Next.js `<Image>` component, providing appropriate props.
    -   [ ] Identify Largest Contentful Paint (LCP) images on key pages and add the `priority` prop to them.

3.  **Audit and Refactor Font Loading (AC: 4.4.2)**
    -   [ ] Verify that `next/font` is being used to load all web fonts in `app/layout.tsx`.
    -   [ ] Remove any manual font loading via `<link>` tags.

4.  **Analyze Bundle and Refactor Dependencies (AC: 4.4.3)**
    -   [ ] Run the bundle analyzer and generate a report.
    -   [ ] Review the report to identify the largest packages.
    -   [ ] Create a list of potential optimizations and implement them if feasible.

5.  **Audit and Refactor for Code Splitting (AC: 4.4.4)**
    -   [ ] Identify large client components that are candidates for lazy loading.
    -   [ ] Refactor them to use `next/dynamic`.

6.  **Measure and Document Performance Scores (AC: 4.4.5)**
    -   [ ] Run PageSpeed Insights on the main pages (Home, Category, Product).
    -   [ ] Document the initial scores.
    -   [ ] After performing optimizations, re-run the tests and document the improved scores.
    -   [ ] Ensure all scores are above 90 or provide a reason if a target cannot be met.

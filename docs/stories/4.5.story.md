---
docType: story
epic: 4
story: 5
title: "[Technical Story] Tối ưu hóa SEO On-page cơ bản"
status: Draft
---

# Story 4.5: [Technical Story] Tối ưu hóa SEO On-page cơ bản

**As a** marketing manager, **I want** the website to be technically optimized for search engines, **so that** our content can be indexed effectively and rank well.

**Note:** This is a technical story that focuses on implementing foundational on-page SEO features. It does not include content creation or keyword strategy.

## Acceptance Criteria

- **4.5.1:** <PERSON><PERSON><PERSON> trang động (sản phẩm, danh mục) có cấu trúc URL thân thiện với người dùng và công cụ tìm kiếm (ví dụ: `/san-pham/ten-san-pham-slug`).
- **4.5.2:** Các trang chính có thẻ `<title>` và `<meta name="description">` được tạo động và tối ưu. Ví dụ: trang sản phẩm sẽ hiển thị tên sản phẩm trong title.
- **4.5.3:** <PERSON><PERSON> liệu có cấ<PERSON> trú<PERSON> (Schema <PERSON>up) theo chuẩn `schema.org` được tự động tạo và nhúng dưới dạng JSON-LD cho các trang sản phẩm (`Product` schema) và trang có đánh giá (`Review` schema).
- **4.5.4:** Một tệp `sitemap.xml` được tự động tạo, bao gồm các URL chính của trang web (trang chủ, danh mục, sản phẩm).
- **4.5.5:** Một tệp `robots.txt` được cấu hình để hướng dẫn các bot của công cụ tìm kiếm.

## Dev Notes

### Technical Approach using Next.js 15

- This story leverages the built-in SEO features of the Next.js App Router.
- **Dynamic Metadata (AC 4.5.2)**: Use the `generateMetadata` function in `page.tsx` files to dynamically create titles and descriptions based on the page data (e.g., product name).
- **JSON-LD Schema (AC 4.5.3)**: In `generateMetadata`, you can add a JSON-LD script to the `other` metadata property. Create helper functions that take product or review data and return the correct JSON-LD object structure.
- **Sitemap (AC 4.5.4)**: Create a `sitemap.ts` file in the `app` directory to dynamically generate the sitemap. This file will need to fetch all product/category slugs from the backend API to build the list of URLs.
- **Robots.txt (AC 4.5.5)**: Create a static `robots.txt` file in the `app` directory.

### File Locations

- **Metadata Logic**: `apps/web/src/app/product/[slug]/page.tsx`, `apps/web/src/app/category/[slug]/page.tsx`, etc.
- **Sitemap Generation**: `apps/web/src/app/sitemap.ts`
- **Robots File**: `apps/web/src/app/robots.txt`
- **Schema Helpers**: `apps/web/src/lib/schema.ts` (a new file to house the JSON-LD generation logic).

## Tasks / Subtasks

1.  **Verify URL Structure (AC 4.5.1)**
    -   [ ] Confirm that the dynamic routes created in previous stories for products and categories result in clean, human-readable URLs. No new code is likely needed here, just verification.

2.  **Implement Dynamic Metadata (AC 4.5.2)**
    -   [ ] In the product detail page (`/product/[slug]/page.tsx`), implement `generateMetadata` to set the page title and description based on the product's name and description.
    -   [ ] In the category page (`/category/[slug]/page.tsx`), implement `generateMetadata` to set the title and description based on the category name.
    -   [ ] Set a default title and description in the root `layout.tsx`.

3.  **Implement JSON-LD Schema Markup (AC 4.5.3)**
    -   [ ] Create a helper function in `lib/schema.ts` that generates a `Product` schema object from a product data object.
    -   [ ] In the product detail page's `generateMetadata` function, call this helper and inject the resulting JSON-LD script.
    -   [ ] (Optional, if time permits) Create a helper to generate `AggregateRating` schema based on product reviews.

4.  **Implement Sitemap Generation (AC: 4.5.4)**
    -   [ ] Create the `apps/web/src/app/sitemap.ts` file.
    -   [ ] Inside this file, fetch all product and category routes from the backend API.
    -   [ ] Map the fetched data to the sitemap entry format.
    -   [ ] Include static pages like `/` and `/cart`.

5.  **Create `robots.txt` (AC: 4.5.5)**
    -   [ ] Create the `apps/web/src/app/robots.txt` file.
    -   [ ] Add basic rules, such as allowing all user agents and pointing to the sitemap location.
        ```
        User-agent: *
        Allow: /
        
        Sitemap: [URL-trang-web]/sitemap.xml
        ```

6.  **Write Tests**
    -   [ ] SEO features are difficult to unit test. Verification will be done primarily by inspecting the page source of the rendered pages and validating the generated `sitemap.xml` and `robots.txt` files.
    -   [ ] Use online tools like the Google Rich Results Test to validate the generated Schema Markup.

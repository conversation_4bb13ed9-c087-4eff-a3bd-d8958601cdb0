---
docType: story
epic: 4
story: 1
title: "[Full-Stack] Triển khai chức năng Mã giảm giá (Coupons)"
status: Draft
---

# Story 4.1: [Full-Stack] Triển khai chức năng Mã giảm giá (Coupons)

**As a** user, **I want to** apply a discount code to my shopping cart, **so that I can** receive a lower price on my purchase.

**Note:** This story focuses on implementing a real, backend-validated coupon system, replacing the mocked logic from Story 3.1.

## Acceptance Criteria

- **4.1.1:** [Backend] Một bảng CSDL `coupons` được tạo để lưu trữ mã giảm giá với các thuộc tính như mã, loại giảm giá (phần trăm/số tiền cố định), gi<PERSON> trị, và trạng thái.
- **4.1.2:** [Backend] Một endpoint API mới `POST /api/coupons/validate` được tạo để kiểm tra tính hợp lệ của một mã giảm giá và trả về mức giảm giá.
- **4.1.3:** [Frontend] Trên trang giỏ hàng/thanh toán, khi người dùng nhập mã và nhấn "Áp dụng", frontend sẽ gọi API validate.
- **4.1.4:** [Frontend] Nếu mã hợp lệ, tổng tiền của giỏ hàng được cập nhật để phản ánh mức giảm giá.
- **4.1.5:** [Frontend] Hiển thị thông báo lỗi nếu mã không hợp lệ hoặc không thể áp dụng.
- **4.1.6:** [Backend] Khi một đơn hàng được tạo (từ Story 3.2), thông tin về mã giảm giá đã được áp dụng phải được lưu lại cùng với đơn hàng.

## Dev Notes

### Backend Implementation

- **Database Schema (AC 4.1.1)**:
    - Create a new table `coupons`.
    ```sql
    CREATE TABLE coupons (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      code TEXT UNIQUE NOT NULL,
      discount_type TEXT NOT NULL, -- 'percentage' or 'fixed'
      discount_value NUMERIC NOT NULL,
      is_active BOOLEAN DEFAULT true,
      expires_at TIMESTAMPTZ,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    ```
    - Add some mock coupon codes to the database for testing.
- **API Specification (AC 4.1.2)**:
    - **Endpoint**: `POST /api/coupons/validate`
    - **Request Body**: `{ "code": "GIAM10", "cartTotal": 1000 }`
    - **Success Response (200 OK)**: `{ "valid": true, "discountAmount": 100, "newTotal": 900 }`
    - **Error Response (400 Bad Request)**: `{ "valid": false, "message": "Mã giảm giá không hợp lệ." }`
- **Architecture**:
    - Create `CouponRepository` and `CouponService`.
    - The `CouponService` will contain the validation logic (check if code exists, is active, not expired, etc.).
    - The `OrderService` (`createOrder` method) needs to be updated to accept a coupon code, re-validate it, and store the discount amount in the `orders` table. The `orders` table will need a new column for this.

### Frontend Implementation

- **State Management (Zustand)**:
    - The `cartStore` needs to be updated.
    - The `applyCoupon` action will now be async and will call the `POST /api/coupons/validate` API.
    - The store should hold the state of the applied coupon (`code`, `discountAmount`).
- **Component Updates**:
    - The `CartSummary` component (from Story 3.1) needs its form updated to call the new `applyCoupon` action.
    - It should be able to display the discount amount and the new total.
    - It must also display error messages returned from the API.

## Tasks / Subtasks

1.  **[Backend] Create Database Migration**
    -   [ ] Create a migration to add the `coupons` table.
    -   [ ] Add new columns to the `orders` table to store `coupon_code` and `discount_amount`.
    -   [ ] Seed the `coupons` table with test data.

2.  **[Backend] Implement Coupon Validation API (AC: 4.1.2)**
    -   [ ] Create `CouponRepository` and `CouponService`.
    -   [ ] Implement the validation logic in the service.
    -   [ ] Create the `POST /api/coupons/validate` endpoint.
    -   [ ] Write integration tests for the validation endpoint with valid and invalid codes.

3.  **[Backend] Update Order Creation Logic (AC: 4.1.6)**
    -   [ ] Modify the `OrderService` to accept an optional `couponCode`.
    -   [ ] If a code is provided, call the `CouponService` to validate it again before creating the order.
    -   [ ] Save the discount information with the order.
    -   [ ] Update integration tests for order creation to include a case with a coupon.

4.  **[Frontend] Update Cart Store (AC: 4.1.3, 4.1.4, 4.1.5)**
    -   [ ] Modify the `cartStore` (Zustand).
    -   [ ] Change the `applyCoupon` action to be async and fetch from the validation API.
    -   [ ] Add state to store the applied coupon details and any validation error messages.
    -   [ ] Update selectors to calculate the final total based on the discount.

5.  **[Frontend] Update Cart UI**
    -   [ ] Update the `CartSummary` component to use the new async action.
    -   [ ] Ensure it correctly displays the discount, the new total, and any error messages.

6.  **[Frontend] Write Tests**
    -   [ ] Update unit tests for the `cartStore`, mocking the coupon validation API call.
    -   [ ] Update integration tests for the cart/checkout page to test the coupon application flow.

---
docType: story
epic: 1
story: 2
title: Triển khai xác thực người dùng cơ bản với Supabase
status: Completed
---

# Story 1.2: Triển khai xác thực người dùng cơ bản với Supabase

**As a** user, **I want to** be able to register and log in to the platform, **so that I can** access personalized features.

## Acceptance Criteria

- **1.2.1:** T<PERSON>ch hợp Supabase cho xác thực người dùng (đăng ký, đăng nhập, đăng xuất).
- **1.2.2:** Người dùng có thể đăng ký tài khoản mới bằng email và mật khẩu.
- **1.2.3:** Người dùng có thể đăng nhập bằng email và mật khẩu đã đăng ký và đăng xuất thành công.
- **1.2.4:** Thô<PERSON> tin hồ sơ người dùng cơ bản (full_name, avatar_url) được tạo trong bảng `profiles` sau khi đăng ký thành công.

## Dev Notes

### Previous Story Insights

- The project is set up as a pnpm monorepo. The frontend application is located at `apps/web` and the backend at `apps/api`. This story will primarily affect the `apps/web` application.

### Data Models

- **`auth.users` (Supabase built-in)**: This table will store the core user credentials (email, password hash).
- **`public.profiles`**: This table will store public user information. A new profile should be created for a user upon successful registration.
    - **Schema**: `id (UUID, FK to auth.users.id), full_name (TEXT), avatar_url (TEXT)`
    - [Source: architecture/database-schema.md]
- **`User` Interface**:
    ```typescript
    interface User {
      id: string;
      email: string;
      full_name?: string;
      avatar_url?: string;
      created_at: string;
    }
    ```
    - This interface should be placed in `packages/shared-types`.
    - [Source: architecture/data-models.md]

### API Specifications

- This story does not involve creating new endpoints in the `apps/api` Express service.
- All authentication logic will be handled on the client-side (`apps/web`) by interacting directly with the Supabase client library (`@supabase/supabase-js`).
- The backend API will need middleware to validate Supabase JWTs for protected routes in future stories. [Source: architecture/backend-architecture.md]

### Component Specifications

- **Login Form**: A form containing email and password fields, a submit button, and a link to the registration page.
- **Registration Form**: A form containing email, password, and full name fields, a submit button, and a link to the login page.
- **Logout Button**: A button that, when clicked, signs the user out.
- These components should be built using the established UI library (shadcn/ui) and follow the defined styling. [Source: architecture/front-end-spec.md, architecture/tech-stack.md]

### File Locations

- **Supabase Client**: Created a singleton Supabase client instance in `apps/web/lib/supabase/client.ts`.
- **Authentication Logic**: Auth-related functions (signIn, signUp, signOut) encapsulated in a context provider within `apps/web/lib/auth/auth-context.tsx`.
- **UI Pages**:
    - Login Page: `apps/web/app/login/page.tsx`
    - Registration Page: `apps/web/app/register/page.tsx`
    - Profile Page: `apps/web/app/profile/page.tsx`
- **Environment Variables**: Supabase URL and Anon Key stored as environment variables in `apps/web/.env.local` and prefixed with `NEXT_PUBLIC_`. Created `.env.example` as a template.

### Testing Requirements

- Write unit/component tests for the Login and Registration forms.
- Tests should cover form submission, input validation, and displaying error messages.
- Mock the Supabase client to test authentication logic without making real API calls.
- [Source: architecture/testing-strategy.md]

### Technical Constraints & Guidance

- Use the official `@supabase/supabase-js` library.
- Follow Supabase's recommended practices for authentication in Next.js (App Router), which may involve using their `auth-helpers` library.
- Upon successful registration, create a corresponding entry in the `profiles` table. This can be done using a Supabase Edge Function triggered by the `auth.users` insertion, or client-side after registration. The trigger-based approach is recommended for robustness.
- Manage user session state globally in the frontend application, for example, using React Context.

## Tasks / Subtasks

1.  **Install Supabase Dependencies (AC: 1.2.1)**
    -   [x] In the `apps/web` package, install `@supabase/supabase-js` and any necessary helper libraries (e.g., `@supabase/ssr`).

2.  **Configure Supabase Client (AC: 1.2.1)**
    -   [x] Create `apps/web/.env.example` để hướng dẫn thiết lập `apps/web/.env.local` với `NEXT_PUBLIC_SUPABASE_URL` và `NEXT_PUBLIC_SUPABASE_ANON_KEY`.
    -   [x] Create the Supabase client instance in `apps/web/lib/supabase/client.ts`.

3.  **Implement Registration Flow (AC: 1.2.2, 1.2.4)**
    -   [x] Create the registration page UI at `apps/web/app/register/page.tsx` with necessary form fields (Email, Password, Full Name).
    -   [x] Implement the `signUp` function that calls the Supabase client.
    -   [x] Implement the logic to create a new `profile` in the database upon successful registration.

4.  **Implement Login/Logout Flow (AC: 1.2.3)**
    -   [x] Create the login page UI at `apps/web/app/login/page.tsx` with Email and Password fields.
    -   [x] Implement the `signIn` function.
    -   [x] Implement a `signOut` function.
    -   [x] Create a component `UserAuthNav` that displays user info and a logout button when logged in, or login/register links when logged out.

5.  **Manage User Session (AC: 1.2.1)**
    -   [x] Create a global state management solution (React Context) to provide user session data throughout the application.
    -   [x] Ensure the application state updates correctly on login and logout.

6.  **Write Tests**
    -   [x] Write component tests for the Login and Register forms, mocking user input and submission.
    -   [x] Write tests for the authentication functions, mocking the Supabase client.

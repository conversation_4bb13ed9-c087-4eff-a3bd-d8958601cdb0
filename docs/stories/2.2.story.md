---
docType: story
epic: 2
story: 2
title: "[Frontend] X<PERSON>y dựng Trang danh mục sản phẩm và chức năng duyệt"
status: Draft
---

# Story 2.2: [Frontend] X<PERSON>y dựng Trang danh mục sản phẩm và chức năng duyệt

**As a** user, **I want to** browse products by category with filtering and sorting options, **so that I can** easily find items of interest.

## Acceptance Criteria

- **2.2.1:** Một trang danh mục sản phẩm động (ví dụ: `/category/[slug]`) được tạo để hiển thị sản phẩm theo bố cục dạng lưới, sử dụng lại component `ProductCard`.
- **2.2.2:** Trang này hiển thị các component cho phép người dùng lọc sản phẩm (ví dụ: `FilterSidebar`) và sắp xếp (ví dụ: `SortDropdown`).
- **2.2.3:** Trang này có component `Pagination` để điều hướng qua các trang kết quả.
- **2.2.4:** Khi người dùng thay đổi bộ lọc hoặc tùy chọn sắp xếp, danh sách sản phẩm được cập nhật bằng cách gọi lại API với các tham số truy vấn (query parameters) tương ứng.

## Dev Notes

### Backend API Dependency (CRITICAL)

- **This is a frontend-only story.** It assumes that the backend API will be updated to support filtering, sorting, and pagination.
- The `GET /api/products` endpoint needs to be enhanced to accept query parameters such as:
    - `?category={slug}`
    - `?brand={brandName}`
    - `?sortBy={price|name|createdAt}&order={asc|desc}`
    - `?page={pageNum}&limit={pageSize}`
- **A separate backend story must be created and completed** to implement these API changes before this frontend story can be fully integrated and tested. For now, development can proceed by mocking the API responses.

### Previous Story Insights

- The `ProductCard` component created in Story 2.1 should be reused here.
- The data fetching hook pattern using TanStack Query from Story 2.1 should be adapted for this page.

### Component Specifications

- **`ProductGrid` (AC: 2.2.1)**: A component that displays a list of `ProductCard`s. It will receive its data from the page.
- **`FilterSidebar` (AC: 2.2.2)**: A sidebar component containing various filter options like checkboxes for "Brand", "Category", and potentially a price range slider. State for selected filters will be managed by the page.
- **`SortDropdown` (AC: 2.2.2)**: A dropdown/select component allowing users to choose sort order (e.g., "Price: Low to High", "Newest Arrivals").
- **`Pagination` (AC: 2.2.3)**: A standard pagination component that shows page numbers and next/previous buttons. It will receive the current page and total pages as props.
- [Source: architecture/frontend-architecture.md, architecture/components.md]

### File Locations

- **Category Page**: `apps/web/src/app/category/[slug]/page.tsx`
- **New Reusable Components**: `packages/ui/src/components/` (e.g., `Pagination`)
- **Product Listing Specific Components**: `apps/web/src/components/product-listing/` (e.g., `FilterSidebar`, `SortDropdown`)
- **Data Fetching Hook**: A new hook `useCategoryProducts` should be created to handle fetching data for this page, including managing filter, sort, and page states.

### State Management

- The state of the filters, sort order, and current page should be managed by the page component (`/category/[slug]/page.tsx`).
- This state should be passed to the data fetching hook.
- It's recommended to sync the filter/sort state with the URL query parameters (e.g., `?sortBy=price_asc`) to allow for shareable links.

## Tasks / Subtasks

1.  **Create UI Components (AC: 2.2.2, 2.2.3)**
    -   [ ] Build the `FilterSidebar` component with placeholder filter options.
    -   [ ] Build the `SortDropdown` component with placeholder sort options.
    -   [ ] Build the `Pagination` component.
    -   [ ] Write component tests for each new component.

2.  **Develop Data Fetching Logic**
    -   [ ] Create a new data fetching hook `useCategoryProducts` that accepts filter, sort, and page states as arguments.
    -   [ ] This hook will use TanStack Query to call the `GET /api/products` endpoint, constructing the appropriate query parameters.
    -   [ ] For now, mock the API responses to simulate filtering/sorting/pagination.

3.  **Build the Category Page (AC: 2.2.1, 2.2.4)**
    -   [ ] Create the dynamic page at `apps/web/src/app/category/[slug]/page.tsx`.
    -   [ ] Manage the state for filters, sorting, and pagination. Update this state based on user interactions with the UI components.
    -   [ ] Use the `useCategoryProducts` hook to fetch data based on the current state.
    -   [ ] Render the `FilterSidebar`, `SortDropdown`, `ProductGrid`, and `Pagination` components with the appropriate data and state.
    -   [ ] Implement logic to sync the component state with URL query parameters.

4.  **Write Tests**
    -   [ ] Write component tests for all new UI components.
    -   [ ] Write an integration test for the Category Page, mocking the data fetching hook and simulating user interactions like applying a filter or changing the page.

---
docType: story
epic: 2
story: 6
title: "[Full-Stack] Hiển thị Đánh giá và Bình luận sản phẩm"
status: Draft
---

# Story 2.6: [Full-Stack] Hiển thị Đánh giá và Bình luận sản phẩm

**As a** user, **I want to** see ratings and reviews from other customers, **so that I can** trust the product quality and make a better decision.

**Note:** This story focuses only on *displaying* existing reviews. Creating and interacting with reviews (liking, replying) will be handled in future stories.

## Acceptance Criteria

- **2.6.1:** [Backend] Một endpoint API mới `GET /api/products/{productId}/reviews` được tạo để trả về danh sách các đánh giá cho một sản phẩm cụ thể.
- **2.6.2:** [Backend] Dữ liệu trả về cho mỗi đánh giá bao gồm thông tin của người viết (tên, avatar) bằng cách join với bảng `profiles`.
- **2.6.3:** [Frontend] Trang chi tiết sản phẩm (`/product/[slug]`) gọi API mới để lấy và hiển thị danh sách các đánh giá.
- **2.6.4:** [Frontend] Một component `StarRating` được hiển thị, thể hiện điểm đánh giá trung bình và tổng số lượng đánh giá.
- **2.6.5:** [Frontend] Một component `ReviewList` được hiển thị, liệt kê chi tiết từng đánh giá (sao, bình luận, tên người viết, ngày viết).

## Dev Notes

### Previous Story Insights

- The backend has an established Repository/Service pattern which should be followed for the new API endpoint.
- The frontend uses TanStack Query for data fetching, and this pattern should be continued.

### Backend Implementation

- **API Specification**:
    - **Endpoint**: `GET /api/products/{productId}/reviews`
    - **Success Response (200 OK)**:
        ```json
        {
          "data": [
            {
              "id": "...",
              "rating": 5,
              "comment": "Sản phẩm tuyệt vời!",
              "created_at": "...",
              "user": {
                "full_name": "Nguyễn Văn A",
                "avatar_url": "..."
              }
            }
          ]
        }
        ```
- **Database Schema**:
    - A `reviews` table needs to be created.
    ```sql
    CREATE TABLE reviews (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
      product_id UUID REFERENCES products(id) ON DELETE CASCADE NOT NULL,
      rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
      comment TEXT,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    ```
- **Repository/Service**:
    - Create `reviewRepository.ts` and `reviewService.ts`.
    - The `reviewRepository` will query the `reviews` table, joining with `profiles` on `user_id`.
- **File Locations**:
    - **Migration**: `apps/api/supabase/migrations/...`
    - **Routes**: `apps/api/src/routes/reviewRoutes.ts` (or add to `productRoutes.ts`).
    - **Service/Repository**: `apps/api/src/services/` and `apps/api/src/repositories/`.

### Frontend Implementation

- **Component Specifications**:
    - **`StarRating`**: A reusable component that takes a rating (e.g., 4.5) and displays it as a series of stars (full, half, empty).
    - **`ReviewList`**: A component that takes an array of review objects and maps them to `ReviewItem` components.
    - **`ReviewItem`**: A component to display a single review, including the star rating, comment, author name, and date.
- **Data Fetching**:
    - Create a new hook `useProductReviews(productId)` that uses TanStack Query to fetch data from the new API endpoint.
- **File Locations**:
    - **Page**: `apps/web/src/app/product/[slug]/page.tsx` will be modified to include the new components.
    - **Components**: New components can be placed in `apps/web/src/components/reviews/`.

## Tasks / Subtasks

1.  **[Backend] Create Database Migration**
    -   [ ] Create a new Supabase migration file to create the `reviews` table as specified.
    -   [ ] Add some mock review data to the database for testing.

2.  **[Backend] Implement Review API Endpoint (AC: 2.6.1, 2.6.2)**
    -   [ ] Create `reviewRepository.ts` with a `findByProductId` method that queries the database.
    -   [ ] Create `reviewService.ts` to handle the business logic.
    -   [ ] Create the `GET /api/products/:productId/reviews` route and connect it to the service.
    -   [ ] Write integration tests for the new endpoint.

3.  **[Frontend] Create Review Components (AC: 2.6.4, 2.6.5)**
    -   [ ] Build the `StarRating` display component.
    -   [ ] Build the `ReviewItem` component.
    -   [ ] Build the `ReviewList` component.
    -   [ ] Write component tests for each new component.

4.  **[Frontend] Integrate Reviews on Product Page (AC: 2.6.3)**
    -   [ ] Create the `useProductReviews` data fetching hook.
    -   [ ] In the product detail page, call the hook to get review data.
    -   [ ] Pass the data to the `ReviewList` and `StarRating` components and render them in an appropriate location on the page.
    -   [ ] Handle loading and error states for the review data.

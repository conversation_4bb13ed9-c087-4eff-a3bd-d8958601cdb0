---
docType: story
epic: 2
story: 4
title: "[Frontend] Triển khai chức năng tìm kiếm sản phẩm"
status: Draft
---

# Story 2.4: [Frontend] Triển khai chức năng tìm kiếm sản phẩm

**As a** user, **I want to** search for products by keywords, **so that I can** quickly find specific items.

## Acceptance Criteria

- **2.4.1:** Một component `SearchBar` được tạo và tích hợp vào header chính của trang web.
- **2.4.2:** Khi người dùng nhập văn bản vào `SearchBar` và nhấn Enter (hoặc click nút tìm kiếm), họ được điều hướng đến trang `/search` với từ khóa được thêm vào URL (ví dụ: `/search?q=my-query`).
- **2.4.3:** Trang `/search` đọ<PERSON> từ khóa từ URL, gọi API để lấy sản phẩm phù hợp và hiển thị kết quả trong một `ProductGrid`.
- **2.4.4:** Trải nghiệm tìm kiếm được tối ưu: input của người dùng được `debounce` để tránh gọi API không cần thiết khi gõ nhanh.

## Dev Notes

### Backend API Dependency (CRITICAL)

- **This is a frontend-only story.** It assumes that the backend API will be updated to support keyword searches.
- The `GET /api/products` endpoint needs to be enhanced to accept a new query parameter:
    - `?q={searchTerm}`
- The backend will need to implement full-text search on the `products` table based on this parameter.
- **A separate backend story must be created and completed** to implement this API change. For now, development can proceed by mocking the API responses.

### Previous Story Insights

- The `ProductGrid` and `ProductCard` components from previous stories should be reused on the search results page.
- The data fetching pattern with TanStack Query should be used.

### Component Specifications

- **`SearchBar` (AC: 2.4.1)**: A reusable form component containing a text input and a search button/icon. It should be placed in the main site header.
- **Search Results Page (AC: 2.4.3)**: A new page at `/search` that:
    - Reads the `q` query parameter from the URL.
    - Displays the search term to the user (e.g., "Search results for 'my-query'").
    - Displays a `ProductGrid` with the fetched results.
    - Handles the case where no results are found.

### UX & Performance Optimizations

- **Debouncing (AC: 2.4.4)**: To provide a "smart" search experience and reduce server load, the input from the user should be debounced. This is typically for live search/autocomplete features. For a simple search-and-redirect, this is less critical but good practice if we plan to add autocomplete later. For this story, we will focus on the redirect flow, but the component can be built with debouncing in mind.
- **State Management**: The `SearchBar` will manage its own input state. The `/search` page will read its state from the URL.

### File Locations

- **Search Page**: `apps/web/src/app/search/page.tsx`
- **SearchBar Component**: `apps/web/src/components/layout/SearchBar.tsx` (as it's part of the main layout).
- **Data Fetching Hook**: A new hook `useProductSearch` should be created to handle fetching data for the search page.

## Tasks / Subtasks

1.  **Create `SearchBar` Component (AC: 2.4.1)**
    -   [ ] Build the `SearchBar` component with an input field and a submit button.
    -   [ ] Implement the logic to take the user's input and navigate to the `/search` page with the correct query parameter on submit.
    -   [ ] Add the `SearchBar` to the main site header component.
    -   [ ] Write component tests for the `SearchBar`.

2.  **Develop Data Fetching Logic for Search**
    -   [ ] Create a new data fetching hook `useProductSearch` that accepts a search query string as an argument.
    -   [ ] This hook will use TanStack Query to call `GET /api/products?q={query}`.
    -   [ ] For now, mock the API response to return a filtered list of products based on the query.

3.  **Build the Search Results Page (AC: 2.4.2, 2.4.3)**
    -   [ ] Create the page at `apps/web/src/app/search/page.tsx`.
    -   [ ] Use Next.js APIs to read the `q` parameter from the URL's search parameters.
    -   [ ] Call the `useProductSearch` hook with the query.
    -   [ ] Render a title indicating what the user searched for.
    -   [ ] Render the `ProductGrid` with the results from the hook.
    -   [ ] Implement a message to show when no results are found.

4.  **Write Tests**
    -   [ ] Write component tests for the `SearchBar`.
    -   [ ] Write an integration test for the Search Results Page, mocking the data fetching hook and providing a search query via the test setup.

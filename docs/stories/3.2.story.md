---
docType: story
epic: 3
story: 2
title: "[Full-Stack] Triển khai <PERSON>uy trình thanh toán (Tạo đơn hàng)"
status: Draft
---

# Story 3.2: [Full-Stack] Triển khai <PERSON>uy trình <PERSON> toán (T<PERSON>o đơn hàng)

**As a** user, **I want to** enter my shipping details and confirm my order, **so that** the system can create an order record in preparation for payment.

**Note:** This story focuses on creating the checkout UI and the backend endpoint to save an order to the database. It **does not** include payment gateway integration (e.g., Stripe), which will be handled in the next story.

## Acceptance Criteria

- **3.2.1:** [Frontend] Một trang thanh toán (`/checkout`) được tạo, hiển thị tóm tắt các mặt hàng từ giỏ hàng.
- **3.2.2:** [Frontend] Trang chứa một biểu mẫu (`CheckoutForm`) để người dùng nhập thông tin giao hàng (tên, địa chỉ, SĐT).
- **3.2.3:** [Frontend] Người dùng có thể chọn một phương thức vận chuyển. Chi phí vận chuyển có thể được giả lập (hardcoded) cho story này.
- **3.2.4:** [Frontend] Khi gửi biểu mẫu, một yêu cầu `POST` được gửi đến `/api/orders` với chi tiết giỏ hàng và thông tin giao hàng.
- **3.2.5:** [Backend] Endpoint `POST /api/orders` được tạo. Nó phải xác thực người dùng đã đăng nhập.
- **3.2.6:** [Backend] Endpoint nhận dữ liệu, tính toán tổng tiền, và tạo một bản ghi `Order` và các bản ghi `OrderItem` liên quan trong CSDL với trạng thái `pending`.
- **3.2.7:** [Frontend] Sau khi tạo đơn hàng thành công, giỏ hàng của người dùng được xóa và họ được chuyển hướng đến trang xác nhận đơn hàng (ví dụ: `/order/confirmation/{orderId}`).

## Dev Notes

### Story Scope & Dependencies

- This story connects the client-side cart from Story 3.1 to the backend.
- **Out of Scope**:
    - Real-time shipping cost calculation (use mock/hardcoded values).
    - Payment processing (the order is created, but not yet paid for).
- **Dependency**: Requires a logged-in user. The `POST /api/orders` endpoint must be a protected route.

### Backend Implementation

- **API Specification**:
    - **Endpoint**: `POST /api/orders`
    - **Request Body**:
        ```json
        {
          "cartItems": [ { "productId": "...", "quantity": 2 } ],
          "shippingAddress": { "recipient_name": "...", "address": "...", ... },
          "shippingMethodId": "..."
        }
        ```
    - **Success Response (201 Created)**:
        ```json
        {
          "orderId": "...",
          "status": "pending",
          "totalAmount": 550.00
        }
        ```
- **Database Schema**:
    - This story will use the `orders` and `order_items` tables. Migrations for these tables need to be created.
    - [Source: architecture/data-models.md, architecture/database-schema.md]
- **Architecture**:
    - Create `OrderRepository` and `OrderService`.
    - The `OrderService`'s `createOrder` method must be transactional to ensure that if creating an `OrderItem` fails, the parent `Order` is also rolled back.
    - The endpoint must be protected. It should extract the `user_id` from the validated JWT token.

### Frontend Implementation

- **Components**:
    - `CheckoutForm`: A form for the shipping address with validation.
    - `ShippingMethodSelector`: A component to select a shipping option.
    - `OrderSummary`: A component on the checkout page to display items and totals.
- **State Management**:
    - The checkout page will read its initial state from the `cartStore` (Zustand) created in 3.1.
    - Use TanStack's `useMutation` hook to handle the `POST` request to `/api/orders`.
    - On successful mutation, call the `clearCart` action from the `cartStore` and programmatically navigate the user.

## Tasks / Subtasks

1.  **[Backend] Create Database Migrations**
    -   [ ] Create a migration to add the `orders` table.
    -   [ ] Create a migration to add the `order_items` table with foreign keys to `orders` and `products`.

2.  **[Backend] Implement Order Creation Logic**
    -   [ ] Create `OrderRepository` with a `create` method that handles the transactional insertion of an order and its items.
    -   [ ] Create `OrderService` to orchestrate the order creation, calculate totals (including mock shipping), and interact with the repository.
    -   [ ] Create the `POST /api/orders` endpoint.
    -   [ ] Add authentication middleware to the endpoint to protect it.

3.  **[Backend] Write Tests**
    -   [ ] Write integration tests for the `POST /api/orders` endpoint, ensuring it creates records correctly and is protected.

4.  **[Frontend] Build Checkout Page UI (AC: 3.2.1, 3.2.2, 3.2.3)**
    -   [ ] Create the `/checkout` page.
    -   [ ] Build the `CheckoutForm` for shipping information.
    -   [ ] Build the `ShippingMethodSelector` with mock options.
    -   [ ] Build the `OrderSummary` component to display cart contents on the page.

5.  **[Frontend] Implement Checkout Logic (AC: 3.2.4, 3.2.7)**
    -   [ ] Create a `useCreateOrder` mutation hook using TanStack Query.
    -   [ ] On form submission, call the mutation with the required data from the cart and form.
    -   [ ] On mutation success, clear the cart and redirect the user to a confirmation page.
    -   [ ] Handle loading and error states from the mutation.

6.  **[Frontend] Write Tests**
    -   [ ] Write component tests for the `CheckoutForm`.
    -   [ ] Write an integration test for the checkout page, mocking the `useCreateOrder` hook and simulating form submission.

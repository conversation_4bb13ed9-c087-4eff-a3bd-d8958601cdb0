---
docType: story
epic: 4
story: 2
title: "[Full-Stack] <PERSON><PERSON><PERSON> dựng cơ chế thu thập khách hàng tiềm năng"
status: Draft
---

# Story 4.2: [Full-Stack] <PERSON><PERSON><PERSON> dựng cơ chế thu thập khách hàng tiềm năng

**As a** marketing manager, **I want to** capture potential customer information via forms and pop-ups, **so that I can** build a lead database for future campaigns.

## Acceptance Criteria

- **4.2.1:** [Backend] M<PERSON>t bảng CSDL `leads` được tạo để lưu trữ thông tin khách hàng tiềm năng (ví dụ: email, tên, nguồn).
- **4.2.2:** [Backend] Một endpoint API công khai (không cần xác thực) `POST /api/leads` được tạo để lưu thông tin lead mới.
- **4.2.3:** [Frontend] Một component `ContactForm` được tạo và có thể đặt ở các vị trí chiến lược như footer hoặc trang liên hệ.
- **4.2.4:** [Frontend] Một component `NewsletterPopup` (dạng modal) được tạo, chứa một form đơn giản để nhập email.
- **4.2.5:** [Frontend] `NewsletterPopup` được kích hoạt và hiển thị cho người dùng một lần mỗi phiên, sau một khoảng thời gian nhất định (ví dụ: 15 giây).

## Dev Notes

### Backend Implementation

- **Database Schema (AC 4.2.1)**:
    - Create a new table `leads`.
    ```sql
    CREATE TABLE leads (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      email TEXT UNIQUE NOT NULL,
      name TEXT,
      source TEXT, -- e.g., 'popup', 'contact_form'
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    ```
- **API Specification (AC 4.2.2)**:
    - **Endpoint**: `POST /api/leads`
    - **Request Body**: `{ "email": "<EMAIL>", "name": "Test User", "source": "popup" }`
    - **Success Response (201 Created)**: `{ "success": true, "message": "Thank you for subscribing!" }`
    - **Error Response (400/409)**: Handles invalid email or duplicate entries.
- **Architecture**:
    - Create `LeadRepository` and `LeadService`.
    - The endpoint should be public (no JWT auth).
    - Include basic validation to ensure the email format is valid and not a duplicate.

### Frontend Implementation

- **Components**:
    - **`ContactForm` (AC 4.2.3)**: A form with fields for name, email, and a message. On submit, it calls the `POST /api/leads` API.
    - **`NewsletterPopup` (AC 4.2.4)**: A modal component containing a simplified form (e.g., only an email field).
- **Popup Trigger Logic (AC 4.2.5)**:
    - A simple `useEffect` with a `setTimeout` can be used in the main layout component to trigger the popup.
    - To show it only once per session, use `sessionStorage` to set a flag after the popup has been shown or closed.
- **State Management**:
    - Use TanStack `useMutation` to handle the form submissions to the `/api/leads` endpoint.
    - A simple React state or context can manage the visibility of the popup.

## Tasks / Subtasks

1.  **[Backend] Create Database Migration**
    -   [ ] Create a migration to add the `leads` table.
    -   [ ] Seed the table with any necessary initial data (if any).

2.  **[Backend] Implement Leads API (AC: 4.2.2)**
    -   [ ] Create `LeadRepository` and `LeadService`.
    -   [ ] Implement the logic to validate and save a new lead. Handle duplicate emails.
    -   [ ] Create the public `POST /api/leads` endpoint.
    -   [ ] Write integration tests for the endpoint.

3.  **[Frontend] Build `ContactForm` Component (AC: 4.2.3)**
    -   [ ] Create the `ContactForm` component.
    -   [ ] Implement the form submission logic using `useMutation`.
    -   [ ] Provide user feedback on success or error.
    -   [ ] Place the form in the site footer (or a new `/contact` page).

4.  **[Frontend] Build `NewsletterPopup` Component (AC: 4.2.4)**
    -   [ ] Create the `NewsletterPopup` modal component with an email form.
    -   [ ] Wire up its submission logic to the `/api/leads` endpoint.

5.  **[Frontend] Implement Popup Trigger Logic (AC: 4.2.5)**
    -   [ ] In a suitable layout component, add the logic to show the `NewsletterPopup` after a 15-second delay.
    -   [ ] Use `sessionStorage` to prevent the popup from appearing more than once per session.
    -   [ ] Ensure the user can close the popup.

6.  **Write Tests**
    -   [ ] Write component tests for `ContactForm` and `NewsletterPopup`.
    -   [ ] Write tests for the popup trigger logic (this may require mocking timers).

---
docType: story
epic: 3
story: 1
title: "[Frontend] X<PERSON>y dựng chức năng Giỏ hàng"
status: Draft
---

# Story 3.1: [Frontend] X<PERSON><PERSON> dựng chức năng Giỏ hàng

**As a** user, **I want to** add products to a shopping cart and manage them, **so that I can** prepare for purchase.

## Acceptance Criteria

- **3.1.1:** Người dùng có thể thêm một sản phẩm (cùng với số lượng đã chọn) vào giỏ hàng từ trang chi tiết sản phẩm.
- **3.1.2:** Một biểu tượng giỏ hàng trên header hiển thị số lượng mặt hàng hiện có trong giỏ.
- **3.1.3:** Khi nhấp vào biểu tượng giỏ hàng, một trang hoặc sidebar (`/cart`) hiển thị chi tiết các mặt hàng: <PERSON><PERSON><PERSON>, t<PERSON><PERSON>, g<PERSON><PERSON>, <PERSON><PERSON>, và tổng tiền cho từng mặt hàng.
- **3.1.4:** <PERSON><PERSON><PERSON><PERSON> trang giỏ hàng, người dùng có thể thay đổi số lượng hoặc xóa một mặt hàng. Tổng tiền của giỏ hàng được cập nhật tương ứng.
- **3.1.5:** Có một trường nhập liệu để người dùng áp dụng mã giảm giá, và tổng tiền được cập nhật (logic xác thực mã sẽ được mock cho story này).
- **3.1.6:** Nội dung giỏ hàng được lưu lại giữa các phiên duyệt web (persisted across sessions).

## Dev Notes

### Technical Approach: Client-Side Cart

- For the MVP, the shopping cart will be implemented entirely on the **client-side**.
- **State Management**: Use **Zustand** for managing the cart state globally. [Source: architecture/frontend-architecture.md]
- **Persistence (AC 3.1.6)**: Use Zustand's persistence middleware to save the cart contents to `localStorage`. This will keep the user's cart even if they close the browser tab.
- **Backend Sync**: Syncing the cart with the database for logged-in users is **out of scope** for this story and will be considered a future enhancement.

### Component Specifications

- **`AddToCartButton` (AC 3.1.1)**: A button on the product detail page. On click, it should call a function from the cart store to add the selected product and quantity. It should provide user feedback (e.g., showing a toast notification "Added to cart").
- **`CartIcon` (AC 3.1.2)**: A component in the main header that displays a cart icon and a badge with the total number of items. Clicking it should navigate to the `/cart` page.
- **`CartView` (AC 3.1.3)**: The main component for the `/cart` page. It will display a list of `CartItem` components and the `CartSummary`.
- **`CartItem` (AC 3.1.4)**: A component to render a single row in the cart, showing product details and including controls (e.g., number input, buttons) to update quantity or a "Remove" button.
- **`CartSummary` (AC 3.1.5)**: A component that displays the subtotal, a form field for a coupon code, any discount applied, and the final total.

### Coupon Code Logic (AC 3.1.5)

- For this story, the coupon logic will be mocked.
- Create a simple hardcoded check in the cart store (e.g., if the code is "GIAM10", apply a 10% discount).
- A future story will implement a backend endpoint (`/api/coupons/validate`) to handle real coupon validation.

### File Locations

- **Zustand Store**: `apps/web/src/store/cartStore.ts`
- **Cart Page**: `apps/web/src/app/cart/page.tsx`
- **Cart Components**: `apps/web/src/components/cart/`

## Tasks / Subtasks

1.  **Install Dependencies**
    -   [ ] Install `zustand` in the `apps/web` package.

2.  **Create Cart Store (AC 3.1.6)**
    -   [ ] Create the Zustand store in `apps/web/src/store/cartStore.ts`.
    -   [ ] Define the state shape: `items: CartItem[]`.
    -   [ ] Implement actions: `addItem`, `updateItemQuantity`, `removeItem`, `applyCoupon`.
    -   [ ] Implement selectors: `getCartTotal`, `getCartItemCount`.
    -   [ ] Configure the persistence middleware to use `localStorage`.

3.  **Integrate "Add to Cart" (AC 3.1.1)**
    -   [ ] Create the `AddToCartButton` component.
    -   [ ] Add it to the product detail page.
    -   [ ] Wire its `onClick` event to the `addItem` action in the cart store.

4.  **Build Cart Page/View (AC 3.1.2, 3.1.3, 3.1.4, 3.1.5)**
    -   [ ] Create the `CartIcon` component and add it to the main header.
    -   [ ] Create the `/cart` page.
    -   [ ] Build the `CartItem` component with controls for quantity and removal, linking them to the store actions.
    -   [ ] Build the `CartSummary` component, including the mock coupon logic.
    -   [ ] Assemble the page using the created components and data from the cart store.

5.  **Write Tests**
    -   [ ] Write unit tests for the Zustand cart store's actions and selectors. Mock `localStorage` for these tests.
    -   [ ] Write component tests for `CartItem` and `CartSummary`.
    -   [ ] Write an integration test for the full cart page, simulating adding, updating, and removing items.

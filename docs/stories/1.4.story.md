---
docType: story
epic: 1
story: 4
title: <PERSON>hi<PERSON><PERSON> lập CI/CD cơ bản
status: Draft
---

# Story 1.4: Thiết lập CI/CD cơ bản

**As a** developer, **I want to** have an automated CI/CD pipeline, **so that I can** build and deploy code changes automatically to their respective hosting platforms.

## Acceptance Criteria

- **1.4.1:** Repository trên GitHub được kết nối với Vercel và Netlify.
- **1.4.2:** Ứng dụng Frontend (`apps/web`) được cấu hình để tự động build và deploy lên Vercel.
- **1.4.3:** Ứng dụng Backend (`apps/api`) được cấu hình để tự động build và deploy dưới dạng Serverless Functions lên Netlify.
- **1.4.4:** Mỗi khi có commit mới vào nh<PERSON>h `main`, cả frontend và backend đều được tự động deploy.
- **1.4.5:** <PERSON><PERSON><PERSON> biến môi trườ<PERSON> (ví dụ: `SUPABASE_URL`) đư<PERSON><PERSON> cấu hình an toàn trên cả Vercel và Netlify.

## Dev Notes

### Clarification on Deployment Platforms

- **Conflict Note**: The original story title and ACs mentioned deploying the frontend to Netlify. However, the `deployment-architecture.md` specifies **Vercel for Frontend** and **Netlify for Backend**. This story will follow the architecture document as the source of truth. The goal is to set up deployment for both services on their designated platforms.

### Previous Story Insights

- The project is a pnpm monorepo. The CI/CD configuration must be able to handle this structure, specifically targeting `apps/web` for the frontend build and `apps/api` for the backend build.

### Deployment Architecture

- **Frontend (`apps/web`)**:
    - **Platform**: Vercel
    - **Configuration**: Connect the GitHub repository to a new Vercel project. Vercel should automatically detect it's a Next.js application.
    - **Build Settings**:
        - **Root Directory**: `apps/web`
        - **Install Command**: `pnpm install` (Vercel may need configuration to use pnpm over npm/yarn)
        - **Build Command**: `pnpm build`
    - [Source: architecture/deployment-architecture.md]

- **Backend (`apps/api`)**:
    - **Platform**: Netlify
    - **Configuration**: Connect the GitHub repository to a new Netlify project.
    - **Build Settings**:
        - **Base directory**: `apps/api`
        - **Functions directory**: The directory where compiled serverless functions are placed (e.g., `apps/api/dist/functions`). This requires a build step to compile TypeScript to JavaScript.
        - **Publish directory**: Not applicable for a pure API backend.
    - A `netlify.toml` file will be required in the root or `apps/api` to configure the build process correctly.
    - [Source: architecture/deployment-architecture.md]

### Environment Variables

- Both Vercel and Netlify dashboards provide a UI for setting environment variables.
- The `SUPABASE_URL` and `SUPABASE_ANON_KEY` will need to be added to both platforms. The backend will also need the `SUPABASE_SERVICE_ROLE_KEY`.
- These variables should not be stored in the repository.

## Tasks / Subtasks

1.  **Connect Repository to Vercel (AC: 1.4.1, 1.4.2)**
    -   [ ] Create a new project on Vercel.
    -   [ ] Connect it to the GitHub repository for this project.
    -   [ ] Configure the project settings:
        -   Set the "Root Directory" to `apps/web`.
        -   Ensure Vercel is configured to use `pnpm`.
    -   [ ] Add required environment variables (`NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`) to the Vercel project settings.

2.  **Connect Repository to Netlify (AC: 1.4.1, 1.4.3)**
    -   [ ] Create a new project on Netlify.
    -   [ ] Connect it to the same GitHub repository.
    -   [ ] Create a `netlify.toml` file to configure the build for the `apps/api` workspace.
        -   Specify the `base` directory as `apps/api`.
        -   Define the `build` command (e.g., `pnpm build`).
        -   Specify the `functions` directory.
    -   [ ] Add required environment variables (`SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`) to the Netlify project settings.

3.  **Configure Backend Build for Netlify Functions (AC: 1.4.3)**
    -   [ ] In `apps/api`, add a build script to `package.json` that compiles the TypeScript source code (e.g., Express routes) into a structure that Netlify can deploy as serverless functions.
    -   [ ] Each API route file might need to be adapted to export a handler compatible with Netlify Functions.

4.  **Trigger Initial Deployments (AC: 1.4.4)**
    -   [ ] Push a commit to the `main` branch.
    -   [ ] Verify that builds are automatically triggered on both Vercel and Netlify.
    -   [ ] Confirm that both deployments complete successfully.

5.  **Verify Deployments (AC: 1.4.2, 1.4.3, 1.4.5)**
    -   [ ] Access the deployed Vercel URL and confirm the frontend application loads.
    -   [ ] Access the deployed Netlify function URL for a public API endpoint (e.g., `GET /api/products`) and confirm it returns data.
    -   [ ] Check the function logs on both platforms to ensure there are no errors related to environment variables.

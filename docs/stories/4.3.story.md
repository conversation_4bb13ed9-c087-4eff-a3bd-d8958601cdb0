---
docType: story
epic: 4
story: 3
title: "[Frontend] <PERSON><PERSON><PERSON> hợp các kênh marketing cơ bản (Analytics & Pixels)"
status: Draft
---

# Story 4.3: [Frontend] T<PERSON><PERSON> hợp các kênh marketing cơ b<PERSON>n (Analytics & Pixels)

**As a** marketing manager, **I want to** integrate analytics and tracking pixels into the website, **so that I can** monitor user behavior, track conversions, and optimize marketing campaigns.

## Acceptance Criteria

- **4.3.1:** Google Analytics (GA4) tracking script is successfully integrated into the application and tracks page views.
- **4.3.2:** Facebook Pixel tracking script is successfully integrated into the application and tracks page views.
- **4.3.3:** A <PERSON><PERSON> (Official Account) chat widget is integrated and visible on the site.
- **4.3.4:** Key conversion events (e.g., "Purchase") are tracked and sent to both Google Analytics and Facebook Pixel upon successful order completion.

## Dev Notes

### Prerequisites

- The following IDs/keys must be provided by the marketing team and stored as environment variables:
    - `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID`
    - `NEXT_PUBLIC_FACEBOOK_PIXEL_ID`
    - `NEXT_PUBLIC_ZALO_OA_ID`

### Technical Approach

- **Script Injection**: Use the `next/script` component to add the third-party scripts. This component provides strategies for loading scripts to optimize performance (e.g., `afterInteractive`).
- **Event Tracking**: Create a unified event tracking service or helper module (e.g., `lib/tracking.ts`) that abstracts the calls to the different analytics platforms. This makes it easy to fire an event to multiple services at once.
    - Example: `trackEvent('purchase', { value: 100, currency: 'VND' })` would call both `gtag(...)` and `fbq(...)`.
- **Zalo Widget**: The Zalo OA chat widget is typically added by embedding a script provided by Zalo.

### File Locations

- **Main Layout**: The primary GA and FB Pixel scripts should be added to the root layout file (`apps/web/src/app/layout.tsx`) so they are present on all pages.
- **Tracking Helper**: `apps/web/src/lib/tracking.ts`
- **Conversion Event Trigger**: The "Purchase" event should be triggered on the order confirmation page (`apps/web/src/app/order/confirmation/[orderId]/page.tsx`) inside a `useEffect` hook.

## Tasks / Subtasks

1.  **Configure Environment Variables**
    -   [ ] Add `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID`, `NEXT_PUBLIC_FACEBOOK_PIXEL_ID`, and `NEXT_PUBLIC_ZALO_OA_ID` to the environment variables file (`apps/web/.env.local`).

2.  **Integrate Google Analytics (AC: 4.3.1)**
    -   [ ] Add the GA4 gtag.js script to the root layout using `next/script`.
    -   [ ] Configure the script with the GA ID from environment variables.
    -   [ ] Verify that page views are being recorded in the Google Analytics dashboard.

3.  **Integrate Facebook Pixel (AC: 4.3.2)**
    -   [ ] Add the Facebook Pixel base code to the root layout using `next/script`.
    -   [ ] Configure the script with the Pixel ID from environment variables.
    -   [ ] Verify that the `PageView` event is being recorded in the Facebook Events Manager.

4.  **Integrate Zalo OA Widget (AC: 4.3.3)**
    -   [ ] Add the Zalo OA chat widget script to the root layout.
    -   [ ] Configure it with the Zalo OA ID.
    -   [ ] Verify that the chat widget appears correctly on the website.

5.  **Implement Conversion Tracking (AC: 4.3.4)**
    -   [ ] Create a tracking helper module (`lib/tracking.ts`) with a function to track purchase events.
    -   [ ] On the order confirmation page, call this tracking function inside a `useEffect` hook that runs once.
    -   [ ] Pass relevant event data (e.g., order value, currency).
    -   [ ] Verify that "Purchase" events appear correctly in both GA and Facebook Events Manager.

6.  **Write Tests**
    -   [ ] Testing third-party scripts directly is often impractical. Focus on testing the application's side of the integration.
    -   [ ] Write a test to ensure the `trackEvent` helper function is called with the correct parameters on the confirmation page. This can be done by mocking the tracking module.

---
docType: story
epic: 3
story: 4
title: "[Full-Stack] <PERSON><PERSON>c nhận đơn hàng và Gửi Email"
status: Draft
---

# Story 3.4: [Full-Stack] Xác nhận đơn hàng và Gửi Email

**As a** user, **I want to** receive a clear confirmation on-screen and via email after my purchase, **so that I** know my order was successful and have a record of it.

## Acceptance Criteria

- **3.4.1:** [Backend] Một endpoint API mới `GET /api/orders/{orderId}` đ<PERSON><PERSON><PERSON> tạo, trả về chi tiết của một đơn hàng. Endpoint này phải được bảo vệ, chỉ cho phép chủ sở hữu của đơn hàng truy cập.
- **3.4.2:** [Backend] Tích hợp dịch vụ gửi email (ví dụ: Resend) và tạo một hàm để gửi email xác nhận đơn hàng.
- **3.4.3:** [Backend] Logic gửi email được kích hoạt sau khi thanh toán được xác nh<PERSON>n (ví dụ: trong webhook của Stripe hoặc sau khi xác nhận COD từ Story 3.3).
- **3.4.4:** [Frontend] Sau khi thanh toán thành công, người dùng được chuyển hướng đến một trang xác nhận đơn hàng động, ví dụ: `/order/confirmation/{orderId}`.
- **3.4.5:** [Frontend] Trang xác nhận gọi API `GET /api/orders/{orderId}` để lấy và hiển thị chi tiết đơn hàng (mã đơn hàng, sản phẩm đã mua, tổng tiền, địa chỉ giao hàng).

## Dev Notes

### Prerequisites

- An account with an email sending service (e.g., Resend) must be created and its API key must be available as an environment variable.

### Backend Implementation

- **API Specification**:
    - **Endpoint**: `GET /api/orders/{orderId}`
    - **Protection**: This route must be protected. The service logic should verify that the `user_id` from the JWT token matches the `user_id` on the order being requested.
    - **Success Response (200 OK)**: A full order object, including nested `order_items` and product details.
- **Email Service Integration**:
    - **Library**: Install the required library (e.g., `resend`).
    - **Service Abstraction**: Create a new `emailService.ts` to encapsulate email sending logic. This service will have a method like `sendOrderConfirmationEmail(userEmail, orderDetails)`.
    - **Email Templates**: Create simple HTML email templates for the confirmation email. These can be stored as template literals or in separate `.html` files.
    - **Triggering**: The `emailService.sendOrderConfirmationEmail` method should be called from within the payment success logic developed in Story 3.3 (e.g., after updating the order status in the Stripe webhook).
- **File Locations**:
    - **Order Endpoint**: `apps/api/src/routes/orderRoutes.ts` (or similar).
    - **Email Service**: `apps/api/src/services/emailService.ts`.

### Frontend Implementation

- **Page**: Create a new dynamic page at `apps/web/src/app/order/confirmation/[orderId]/page.tsx`.
- **Components**:
    - `OrderConfirmationDetails`: A component to display the main details of the confirmed order.
    - `OrderedProductList`: A component to list the items that were in the order.
- **Data Fetching**:
    - Create a new hook `useOrder(orderId)` that uses TanStack Query to fetch data from the `GET /api/orders/{orderId}` endpoint.
    - The page will get the `orderId` from the URL parameters.

## Tasks / Subtasks

1.  **[Backend] Configure Email Service**
    -   [ ] Sign up for an email service like Resend.
    -   [ ] Install the necessary SDK (e.g., `pnpm add resend` in `apps/api`).
    -   [ ] Add the API key to environment variables.
    -   [ ] Create the `emailService.ts` with a method to send emails.

2.  **[Backend] Implement `GET /orders/{orderId}` Endpoint (AC: 3.4.1)**
    -   [ ] Create the route `GET /api/orders/:orderId`.
    -   [ ] In `orderService.ts`, create a `findOrderById` method.
    -   [ ] The service must check if the authenticated user's ID matches the order's `user_id` before returning data.
    -   [ ] The repository method should fetch the order and its related items.
    -   [ ] Write integration tests for this endpoint, including tests for the authorization logic.

3.  **[Backend] Trigger Confirmation Email (AC: 3.4.3)**
    -   [ ] In the payment success logic from Story 3.3 (e.g., Stripe webhook), after updating the order status, call the `emailService.sendOrderConfirmationEmail` method.
    -   [ ] Pass the necessary order and user details to the email service.

4.  **[Frontend] Build Confirmation Page (AC: 3.4.4, 3.4.5)**
    -   [ ] Create the dynamic page `apps/web/src/app/order/confirmation/[orderId]/page.tsx`.
    -   [ ] Create the `useOrder(orderId)` hook to fetch order details.
    -   [ ] Build the necessary components to display the order details in a user-friendly way.
    -   [ ] Handle loading and error states (e.g., what if the order ID is invalid or doesn't belong to the user?).

5.  **[Frontend] Update Checkout Flow (AC: 3.4.4)**
    -   [ ] In the checkout logic from Story 3.2/3.3, ensure that upon successful order creation/payment, the user is redirected to the new confirmation page with the correct `orderId`.

6.  **[Frontend] Write Tests**
    -   [ ] Write component tests for the new confirmation page components.
    -   [ ] Write an integration test for the confirmation page, mocking the `useOrder` hook.

---
docType: story
epic: 1
story: 3
title: <PERSON><PERSON>ản lý dữ liệu sản phẩm ban đầu
status: Draft
---

# Story 1.3: Quản lý dữ liệu sản phẩm ban đầu

**As an** administrator, **I want to** be able to add, view, update, and delete product information, **so that I can** manage the product catalog.

## Acceptance Criteria

- **1.3.1:** Schema cho bảng `products` được thiết lập trong Supabase.
- **1.3.2:** API backend (trong `apps/api`) cung cấp các endpoint CRUD (Create, Read, Update, Delete) để quản lý sản phẩm.
- **1.3.3:** <PERSON><PERSON> một script hoặc cơ chế để seed (nhập) dữ liệu sản phẩm mẫu vào database.

## Dev Notes

### Previous Story Insights

- The project is a pnpm monorepo. This story focuses on building the backend product API in the `apps/api` package.
- Authentication is handled by Supabase. While these initial CRUD endpoints can be public, subsequent stories will require adding authentication middleware to protect `POST`, `PUT`, `DELETE` operations.

### Data Models

- **`Product` Interface**: This is the core data model for this story.
    ```typescript
    interface Product {
      id: string;
      name: string;
      description: string;
      price: number;
      image_urls: string[];
      brand: string;
      category: string;
      inventory_count: number;
      created_at: string;
    }
    ```
    - This interface should be defined in `packages/shared-types` to be accessible by both frontend and backend.
    - [Source: architecture/data-models.md]

### Database Schema

- A table named `products` must be created in the Supabase (PostgreSQL) database.
- **Schema SQL**:
    ```sql
    CREATE TABLE products (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name TEXT NOT NULL,
      description TEXT,
      price NUMERIC(10, 2) NOT NULL,
      image_urls TEXT[],
      brand TEXT,
      category TEXT,
      inventory_count INTEGER NOT NULL DEFAULT 0,
      created_at TIMESTAMPTZ DEFAULT NOW()
    );
    ```
- This can be added as a new migration file using the Supabase CLI.
- [Source: architecture/database-schema.md]

### API Specifications

- The REST API endpoints should be implemented in the `apps/api` Express application.
- **Endpoints**:
    - `GET /api/products`: Lấy danh sách tất cả sản phẩm (có thể có phân trang trong tương lai).
    - `GET /api/products/{productId}`: Lấy chi tiết một sản phẩm theo ID.
    - `POST /api/products`: Tạo một sản phẩm mới.
    - `PUT /api/products/{productId}`: Cập nhật thông tin một sản phẩm.
    - `DELETE /api/products/{productId}`: Xóa một sản phẩm.
- [Source: architecture/api-specification.md]

### Backend Architecture

- The implementation must follow the **Repository Pattern**.
- **Repository (`ProductRepository`)**: Lớp này sẽ chứa tất cả logic truy vấn trực tiếp đến CSDL Supabase cho bảng `products`. Nó sẽ được inject vào lớp Service.
- **Service (`ProductService`)**: Lớp này chứa logic nghiệp vụ (validation, transformation, etc.) và gọi các phương thức từ `ProductRepository`. Nó không tương tác trực tiếp với CSDL.
- **Controller/Routes**: Các tệp route của Express sẽ xử lý request/response HTTP và gọi các phương thức từ `ProductService`.
- [Source: architecture/backend-architecture.md, architecture/coding-standards.md]

### File Locations

- **Shared Type**: `packages/shared-types/src/product.ts`
- **API Routes**: `apps/api/src/routes/productRoutes.ts`
- **Service Layer**: `apps/api/src/services/productService.ts`
- **Repository Layer**: `apps/api/src/repositories/productRepository.ts`
- **Database Migrations**: `apps/api/supabase/migrations/<timestamp>_create_products_table.sql` (assuming Supabase CLI usage)
- **Seed Script**: `apps/api/src/db/seed.ts`

### Testing Requirements

- **Unit Tests**: Viết unit test cho lớp `ProductService`. Mock lớp `ProductRepository` để cô lập service khỏi CSDL.
- **Integration Tests**: Viết integration test cho các API endpoint bằng Supertest. Các bài test này sẽ gọi đến API thật (trong môi trường test) và kiểm tra response.
- [Source: architecture/testing-strategy.md]

## Tasks / Subtasks

1.  **Create Database Migration (AC: 1.3.1)**
    -   [ ] Sử dụng Supabase CLI, tạo một migration mới để tạo bảng `products` với schema đã định nghĩa.
    -   [ ] Áp dụng migration vào database Supabase local (và staging nếu có).

2.  **Implement Repository Layer (AC: 1.3.2)**
    -   [ ] Tạo tệp `apps/api/src/repositories/productRepository.ts`.
    -   [ ] Implement các hàm CRUD: `findAll`, `findById`, `create`, `update`, `delete` sử dụng Supabase client để tương tác với bảng `products`.

3.  **Implement Service Layer (AC: 1.3.2)**
    -   [ ] Tạo tệp `apps/api/src/services/productService.ts`.
    -   [ ] Tạo lớp `ProductService` nhận `ProductRepository` làm dependency.
    -   [ ] Implement các phương thức nghiệp vụ tương ứng, gọi đến repository.

4.  **Implement API Routes (AC: 1.3.2)**
    -   [ ] Tạo tệp `apps/api/src/routes/productRoutes.ts`.
    -   [ ] Định nghĩa các route Express cho `GET /products`, `GET /products/:id`, `POST /products`, `PUT /products/:id`, `DELETE /products/:id`.
    -   [ ] Liên kết các route này với các phương thức trong `ProductService`.
    -   [ ] Tích hợp router này vào file Express chính (`apps/api/src/index.ts`).

5.  **Create Seed Script (AC: 1.3.3)**
    -   [ ] Tạo một tệp script (`apps/api/src/db/seed.ts`) để thêm một vài sản phẩm mẫu vào database.
    -   [ ] Script này nên có thể chạy độc lập từ command line.
    -   [ ] Thêm một script vào `apps/api/package.json` để chạy seed script (ví dụ: `pnpm db:seed`).

6.  **Write Tests**
    -   [ ] Viết unit test cho `ProductService`, mock repository.
    -   [ ] Viết integration test cho các endpoint sản phẩm bằng Supertest.

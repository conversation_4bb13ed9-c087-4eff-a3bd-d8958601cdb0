---
docType: story
epic: 2
story: 5
title: "[Backend] Nâng cấp API sản phẩm để hỗ trợ Tìm kiếm bằng từ khóa"
status: Draft
---

# Story 2.5: [Backend] Nâng cấp API sản phẩm để hỗ trợ Tìm kiếm bằng từ khóa

**As a** frontend developer, **I want** the `/api/products` endpoint to support a query parameter for keyword searching, **so that I can** build a search results page as defined in Story 2.4.

## Acceptance Criteria

- **2.5.1:** `GET /api/products` endpoint chấp nhận một tham số truy vấn `q` để thực hiện tìm kiếm toàn văn (full-text search) trên các sản phẩm.
- **2.5.2:** Chức năng tìm kiếm hoạt động hiệu quả trên các trường `name`, `description`, và `brand` của sản phẩm.
- **2.5.3:** Chức năng tìm kiếm có thể kết hợp với các tham số đã có từ Story 2.3 (lọc, sắp xếp, phân trang). Ví dụ: `GET /api/products?category=boots&q=nike&sortBy=price`.
- **2.5.4:** Các bài kiểm thử tích hợp được cập nhật để xác thực chức năng tìm kiếm.

## Dev Notes

### Story Dependency

- This story is a **direct dependency** for the frontend **Story 2.4**.

### Previous Story Insights

- Story 2.3 enhanced the `GET /api/products` endpoint with filtering, sorting, and pagination. This story adds search functionality to that same endpoint.
- The implementation will modify `productRepository.ts` and `productService.ts`.

### Database & Search Implementation

- To implement search efficiently, **PostgreSQL's Full-Text Search (FTS)** must be used. This is more performant and flexible than using `LIKE` or `ILIKE`.
- **Migration/Schema Change**:
    1.  A new column of type `tsvector` needs to be added to the `products` table. This column will store the searchable text content.
    2.  A trigger should be created to automatically update this `tsvector` column whenever the `name`, `description`, or `brand` of a product is inserted or updated.
    3.  A GIN index must be created on the `tsvector` column to ensure search queries are fast.
- **Repository (`productRepository.ts`)**:
    - The `findAll` method needs to be updated.
    - If a `q` parameter is provided, the Supabase query must be modified to include a `.textSearch()` or `.rpc()` call that utilizes the FTS index.
    - The search query should use a function like `websearch_to_tsquery()` for better handling of multi-word user input.
- [Source: architecture/database-schema.md, architecture/backend-architecture.md]

### API Specifications

- The `GET /api/products` endpoint is modified.
- **New Query Parameter**:
    - `q: string` (The search term)
- The response structure remains the same as in Story 2.3 (an object with `data` and `pagination`).

### File Locations

- **Database Migrations**: A new migration file in `apps/api/supabase/migrations/` to add the FTS column, trigger, and index.
- **API Routes**: `apps/api/src/routes/productRoutes.ts` (to pass the `q` param).
- **Service Layer**: `apps/api/src/services/productService.ts` (to handle the `q` param).
- **Repository Layer**: `apps/api/src/repositories/productRepository.ts` (to implement the FTS query logic).

### Testing Requirements

- Update the integration tests for `GET /api/products`.
- Add new test cases to cover:
    - Searching for a product by name.
    - Searching for a product by a word in its description.
    - Combining search with a category filter.
    - Searching for a term that yields no results.
- [Source: architecture/testing-strategy.md]

## Tasks / Subtasks

1.  **Create Database Migration for FTS (AC: 2.5.2)**
    -   [ ] Create a new Supabase migration file.
    -   [ ] In the migration, add a `tsvector` column to the `products` table.
    -   [ ] Create a database function and trigger to automatically populate the `tsvector` column from `name`, `description`, and `brand`.
    -   [ ] Create a GIN index on the new `tsvector` column.
    -   [ ] Apply the migration.

2.  **Update Repository Layer (AC: 2.5.1, 2.5.3)**
    -   [ ] Modify the `findAll` method in `productRepository.ts` to accept the `q` search parameter.
    -   [ ] If `q` is present, add a `.textSearch()` condition to the Supabase query, targeting the `tsvector` column.
    -   [ ] Ensure the search condition works correctly with the existing filter, sort, and pagination logic.

3.  **Update Service & Route Layers**
    -   [ ] Update `productService.ts` to accept and pass the `q` parameter to the repository.
    -   [ ] Update `productRoutes.ts` to read the `q` parameter from `req.query`.

4.  **Update Integration Tests (AC: 2.5.4)**
    -   [ ] Add new integration tests to verify that the search functionality works as expected, both standalone and in combination with other parameters.

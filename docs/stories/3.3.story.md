---
docType: story
epic: 3
story: 3
title: "[Full-Stack] <PERSON><PERSON><PERSON> hợ<PERSON> (COD & Stripe)"
status: Draft
---

# Story 3.3: [Full-Stack] T<PERSON>ch hợ<PERSON> (COD & Stripe)

**As a** user, **I want to** pay for my order using either Cash on Delivery (COD) or a credit card, **so that I can** complete my purchase flexibly and securely.

## Acceptance Criteria

- **3.3.1:** [Frontend] Trên trang thanh toán, sau khi tạo đơn hàng (từ Story 3.2), người dùng thấy các lựa chọn thanh toán: "<PERSON>h toán khi nhận hàng (COD)" và "Thẻ tín dụng".
- **3.3.2:** [COD Flow] Nếu người dùng chọn COD và xác nhận, trạng thái đơn hàng trong CSDL được cập nhật thành `processing`, và người dùng được chuyển đến trang đặt hàng thành công.
- **3.3.3:** [Stripe Flow - Backend] Một endpoint mới `POST /api/orders/{orderId}/create-payment-intent` được tạo để tương tác với Stripe và tạo ra một Payment Intent.
- **3.3.4:** [Stripe Flow - Frontend] Khi người dùng chọn "Thẻ tín dụng", frontend gọi API ở trên và sử dụng `client_secret` trả về để render Stripe Elements (biểu mẫu nhập thông tin thẻ an toàn).
- **3.3.5:** [Stripe Flow - Frontend] Sau khi người dùng gửi thông tin thẻ, frontend xác nhận thanh toán với Stripe.
- **3.3.6:** [Stripe Flow - Backend] Một endpoint webhook `POST /api/webhooks/stripe` được tạo để nhận thông báo từ Stripe khi thanh toán thành công và cập nhật trạng thái đơn hàng trong CSDL.

## Dev Notes

### Prerequisites

- A Stripe account must be created and API keys (Publishable Key, Secret Key, Webhook Signing Secret) must be available as environment variables.

### Story Scope

- This story builds directly on Story 3.2. The flow starts *after* an order has been created with a `pending` status.
- This story covers only COD and Credit Card payments via Stripe. Other methods (Bank Transfer, E-wallets) are out of scope.

### Backend Implementation

- **API Endpoints**:
    1.  `POST /api/orders/{orderId}/pay`: This could be an alternative to `create-payment-intent`. The body would contain `{ paymentMethod: 'cod' }`. This endpoint would handle the logic for non-Stripe payments.
    2.  `POST /api/orders/{orderId}/create-payment-intent`: Creates a payment intent for Stripe.
    3.  `POST /api/webhooks/stripe`: Handles events from Stripe. This endpoint must not require JWT authentication but must validate the request signature from Stripe.
- **Stripe Integration**:
    - Use the official `stripe` Node.js library.
    - The `create-payment-intent` service should retrieve the order total from the database to ensure the correct amount is charged.
    - The webhook handler must be robust: verify the Stripe signature, check for event idempotency, and handle potential errors gracefully.
- **File Locations**:
    - **Stripe Service**: `apps/api/src/services/stripeService.ts`
    - **Webhook Route**: `apps/api/src/routes/webhookRoutes.ts`
    - **Payment Logic**: `apps/api/src/services/paymentService.ts` or extend `orderService.ts`.

### Frontend Implementation

- **Dependencies**:
    - Install `@stripe/stripe-js` and `@stripe/react-stripe-js`.
- **Components**:
    - `PaymentMethodSelector`: A component with radio buttons for COD and Credit Card.
    - `StripeCheckoutForm`: A component that wraps the Stripe Elements provider and the payment form. It will be displayed only when the "Credit Card" option is selected.
- **Stripe Integration Flow**:
    1.  Load Stripe.js with the publishable key.
    2.  When the user proceeds to pay with a card, call the backend to get the `client_secret`.
    3.  Wrap the payment form with `<Elements>` provider from Stripe.
    4.  Use Stripe hooks (`useStripe`, `useElements`) to securely submit the payment.
    5.  Handle the payment result and show appropriate messages (success, error) to the user.

## Tasks / Subtasks

1.  **[Backend] Install Dependencies & Configure**
    -   [ ] Install the `stripe` library in `apps/api`.
    -   [ ] Add Stripe API keys and webhook secret to environment variables.

2.  **[Backend] Implement COD Flow (AC: 3.3.2)**
    -   [ ] Create or update an endpoint (e.g., `POST /api/orders/{orderId}/confirm-cod`) that updates the order status to `processing`.
    -   [ ] Protect this endpoint with authentication.

3.  **[Backend] Implement Stripe Payment Intent (AC: 3.3.3)**
    -   [ ] Create the `POST /api/orders/{orderId}/create-payment-intent` endpoint.
    -   [ ] The service for this endpoint should read the order amount from the DB and create a Stripe Payment Intent for that amount.
    -   [ ] Return the `client_secret` from the Payment Intent.

4.  **[Backend] Implement Stripe Webhook (AC: 3.3.6, 3.3.7)**
    -   [ ] Create the `POST /api/webhooks/stripe` endpoint.
    -   [ ] Implement logic to verify the Stripe request signature using the webhook secret.
    -   [ ] Handle the `payment_intent.succeeded` event: extract the order ID from metadata and update the order status in the database.

5.  **[Frontend] Install Dependencies**
    -   [ ] Install `@stripe/stripe-js` and `@stripe/react-stripe-js` in `apps/web`.

6.  **[Frontend] Implement Payment Selection UI (AC: 3.3.1, 3.3.4)**
    -   [ ] On the checkout page, add the `PaymentMethodSelector` component.
    -   [ ] Create a `StripeCheckoutForm` component that is conditionally rendered.
    -   [ ] When the user selects "Credit Card", fetch the `client_secret` from the backend and pass it to the `StripeCheckoutForm`.

7.  **[Frontend] Implement Stripe Elements Form (AC: 3.3.5)**
    -   [ ] Inside `StripeCheckoutForm`, use the Stripe Elements provider and components (`PaymentElement` or individual card elements).
    -   [ ] Implement the form submission logic using `stripe.confirmPayment()`.
    -   [ ] Handle the result of the payment confirmation and redirect to the success/failure page.

8.  **[Frontend] Implement COD Confirmation**
    -   [ ] When the user selects "COD" and clicks "Place Order", call the backend endpoint from Task 2.
    -   [ ] On success, redirect to the order confirmation page.

9.  **Write Tests**
    -   [ ] Write integration tests for the new backend endpoints. Mock the Stripe API calls.
    -   [ ] Write component tests for the `PaymentMethodSelector`. Mocking Stripe Elements on the frontend for automated testing can be complex; focus on testing the state logic that controls the payment flow.

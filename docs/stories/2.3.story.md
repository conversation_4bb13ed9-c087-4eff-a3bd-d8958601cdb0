---
docType: story
epic: 2
story: 3
title: "[Backend] <PERSON><PERSON><PERSON> cấp API sản phẩm để hỗ trợ <PERSON>, <PERSON><PERSON><PERSON> xế<PERSON>, <PERSON><PERSON> trang"
status: Draft
---

# Story 2.3: [Backend] <PERSON>âng cấp API sản phẩm để hỗ trợ <PERSON>, <PERSON><PERSON><PERSON> xếp, <PERSON><PERSON> trang

**As a** frontend developer, **I want** the `/api/products` endpoint to support query parameters for filtering, sorting, and pagination, **so that I can** build a dynamic product category page as defined in Story 2.2.

## Acceptance Criteria

- **2.3.1:** `GET /api/products` endpoint chấp nhận các tham số truy vấn (query parameters) để lọc sản phẩm, ví dụ: `category`, `brand`.
- **2.3.2:** `GET /api/products` endpoint chấp nhận các tham số `sortBy` (ví dụ: `price`, `createdAt`) và `order` (`asc`, `desc`) để sắp xếp kết quả.
- **2.3.3:** `GET /api/products` endpoint chấp nhận các tham số `page` và `limit` để hỗ trợ phân trang.
- **2.3.4:** Response của API trả về một object chứa cả danh sách sản phẩm (`data`) và thông tin phân trang (`pagination: { total, totalPages, currentPage, limit }`).
- **2.3.5:** Các bài kiểm thử tích hợp (integration tests) được cập nhật để xác thực các chức năng lọc, sắp xếp và phân trang.

## Dev Notes

### Story Dependency

- This story is a **direct dependency** for the frontend **Story 2.2**. It must be completed to allow the full implementation of the product category page.

### Previous Story Insights

- The initial CRUD endpoints for products were created in Story 1.3 within `apps/api`. This story will modify the `findAll` method in the repository and service layers.
- The project uses the Repository Pattern. All database query modifications should happen within `productRepository.ts`.

### API Specifications

- The `GET /api/products` endpoint needs to be updated.
- **Query Parameters**:
    - `category: string`
    - `brand: string`
    - `sortBy: 'price' | 'name' | 'createdAt'` (default to `createdAt`)
    - `order: 'asc' | 'desc'` (default to `desc`)
    - `page: number` (default to `1`)
    - `limit: number` (default to `10`)
- **Success Response (200 OK)**:
    ```json
    {
      "data": [
        { "id": "...", "name": "...", ... }
      ],
      "pagination": {
        "total": 100,
        "totalPages": 10,
        "currentPage": 1,
        "limit": 10
      }
    }
    ```
- The OpenAPI specification in `docs/architecture/api-specification.md` should be updated to reflect these new parameters.

### Backend Architecture

- **`productRepository.ts`**: The `findAll` method must be updated to dynamically build a Supabase query based on the provided filter, sort, and pagination parameters. It should also execute a `count` query to get the total number of matching products for the pagination metadata.
- **`productService.ts`**: The service layer will receive the query parameters from the controller, validate them, and pass them to the repository. It will then format the response to match the structure defined in the API specifications.
- **`productRoutes.ts`**: The route handler for `GET /products` will need to parse the query parameters from the request object (`req.query`) and pass them to the service.
- [Source: architecture/backend-architecture.md]

### File Locations

- **API Routes**: `apps/api/src/routes/productRoutes.ts`
- **Service Layer**: `apps/api/src/services/productService.ts`
- **Repository Layer**: `apps/api/src/repositories/productRepository.ts`

### Testing Requirements

- Update the integration tests for `GET /api/products` in the backend.
- Add new test cases to cover:
    - Filtering by category and brand.
    - Sorting by price (asc/desc).
    - Pagination logic (requesting page 2, etc.).
    - The structure of the response object, ensuring it contains the `pagination` key.
- [Source: architecture/testing-strategy.md]

## Tasks / Subtasks

1.  **Update Repository Layer (AC: 2.3.1, 2.3.2, 2.3.3)**
    -   [ ] Modify the `findAll` method in `productRepository.ts` to accept an options object containing filter, sort, and pagination parameters.
    -   [ ] Dynamically construct the Supabase query using `.eq()`, `.order()`, and `.range()` based on the provided options.
    -   [ ] Implement logic to get the total count of filtered items for pagination metadata.
    -   [ ] Return both the data and the total count.

2.  **Update Service Layer (AC: 2.3.4)**
    -   [ ] Modify `productService.ts` to accept the query parameters.
    -   [ ] Add validation for the parameters (e.g., parse numbers, set defaults).
    -   [ ] Call the updated repository method.
    -   [ ] Calculate `totalPages` and format the final response object, including the `pagination` metadata.

3.  **Update Route Layer (AC: 2.3.1, 2.3.2, 2.3.3)**
    -   [ ] In `productRoutes.ts`, update the `GET /products` handler to read the query parameters from `req.query`.
    -   [ ] Pass the parsed parameters to the `productService`.

4.  **Update API Documentation**
    -   [ ] Update the OpenAPI YAML in `docs/architecture/api-specification.md` to document the new query parameters for the `GET /products` endpoint.

5.  **Update Integration Tests (AC: 2.3.5)**
    -   [ ] Open the existing integration test file for product routes.
    -   [ ] Add new tests to verify that filtering by `category` works.
    -   [ ] Add new tests to verify that sorting by `price` (asc and desc) works.
    -   [ ] Add new tests to verify that pagination (`page` and `limit`) works and that the `pagination` object in the response is correct.

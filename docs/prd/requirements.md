# Requirements

## Functional

*   FR1: <PERSON><PERSON> thống phải hiển thị trang chủ với Hero Banner, khu vực sản phẩm nổi bật/b<PERSON> ch<PERSON>, thương hiệu nổi bật, banner khuyến mãi và đánh giá từ khách hàng.
*   FR2: <PERSON><PERSON> thống phải cho phép người dùng duyệt sản phẩm theo bố cục dạng lưới, với các tùy chọn phân trang hoặc cuộn vô hạn.
*   FR3: Hệ thống phải cung cấp chức năng bộ lọc và sắp xếp sản phẩm cơ bản trên trang danh mục.
*   FR4: Hệ thống phải hiển thị trang chi tiết sản phẩm với thư viện hình ảnh/video chất lư<PERSON> cao, thông tin sản phẩm chi tiế<PERSON>, n<PERSON><PERSON> kê<PERSON> gọ<PERSON>nh độ<PERSON> (CTA), thông tin vận chuyển/đổi trả và phần đánh giá/bình luận.
*   FR5: Hệ thống phải cung cấp thanh tìm kiếm thông minh để người dùng tìm kiếm sản phẩm.
*   FR6: Hệ thống phải cho phép người dùng đăng ký tài khoản, đăng nhập và quản lý hồ sơ cá nhân.
*   FR7: Hệ thống phải hiển thị lịch sử đơn hàng của người dùng.
*   FR8: Hệ thống phải tích hợp xác thực người dùng thông qua Supabase.
*   FR9: Hệ thống phải hiển thị giỏ hàng rõ ràng và trực quan, cho phép người dùng thêm/xóa sản phẩm và cập nhật số lượng.
*   FR10: Hệ thống phải hỗ trợ tính năng tiện ích trong giỏ hàng (ví dụ: áp dụng mã giảm giá).
*   FR11: Hệ thống phải cung cấp quy trình thanh toán đơn giản, một trang.
*   FR12: Hệ thống phải thu thập thông tin giao hàng và cho phép người dùng chọn phương thức vận chuyển.
*   FR13: Hệ thống phải hỗ trợ các phương thức thanh toán: COD, chuyển khoản ngân hàng, ví điện tử, thẻ tín dụng/ghi nợ.
*   FR14: Hệ thống phải hiển thị trang xác nhận đơn hàng và trang "Đặt hàng thành công" sau khi thanh toán.
*   FR15: Hệ thống phải cho phép người dùng đánh giá sản phẩm bằng hệ thống sao và viết bình luận chi tiết.
*   FR16: Hệ thống phải hỗ trợ tương tác với bình luận (ví dụ: thích, trả lời).
*   FR17: Hệ thống phải hiển thị các chương trình khuyến mãi và ưu đãi.
*   FR18: Hệ thống phải có các cơ chế thu thập và quản lý khách hàng tiềm năng (ví dụ: pop-up, form liên hệ).
*   FR19: Hệ thống phải tích hợp với các kênh marketing (Google Analytics, Facebook Pixel, Zalo OA, SEO, SEM).
*   FR20: Hệ thống phải hỗ trợ chương trình giới thiệu (referral program).

## Non Functional

*   NFR1: Hệ thống phải có tốc độ tải trang nhanh (Core Web Vitals đạt điểm 'Good') trên cả thiết bị di động và máy tính.
*   NFR2: Hệ thống phải được tối ưu hóa cho thiết bị di động (Mobile-first Indexing).
*   NFR3: Hệ thống phải sử dụng HTTPS để đảm bảo bảo mật dữ liệu.
*   NFR4: Hệ thống phải có cấu trúc URL thân thiện với SEO.
*   NFR5: Hệ thống phải hỗ trợ các thẻ Meta và tiêu đề tối ưu cho SEO.
*   NFR6: Hệ thống phải sử dụng Schema Markup (Structured Data) cho các sản phẩm và đánh giá.
*   NFR7: Hệ thống phải có Sitemap và Robots.txt được cấu hình đúng.
*   NFR8: Hệ thống phải có khả năng mở rộng để xử lý lượng truy cập và dữ liệu tăng lên trong tương lai.
*   NFR9: Hệ thống phải đảm bảo bảo mật thông tin tài khoản người dùng và giao dịch thanh toán.
*   NFR10: Hệ thống phải có thời gian hoạt động (uptime) tối thiểu 99.9%.
*   NFR11: Hệ thống phải có khả năng phục hồi sau lỗi và sao lưu dữ liệu định kỳ.

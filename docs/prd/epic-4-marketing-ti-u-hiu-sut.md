# Epic 4 Marketing & Tối ưu hiệu suất

Epic này nhằm tích hợp các công cụ marketing và khuyến mãi cơ bản để thu hút và giữ chân khách hàng, đồng thời tối ưu hóa toàn diện hiệu suất tải trang và khả năng hiển thị trên công cụ tìm kiếm (SEO), đả<PERSON> bảo nền tảng hoạt động <PERSON> chóng, mư<PERSON>t mà và dễ dàng được tìm thấy.

## Story 4.1 Triển khai các chương trình khuyến mãi và ưu đãi cơ bản

As a marketing manager, I want to create and manage basic promotions and offers, so that I can attract and retain customers.

## Acceptance Criteria

4.1.1: <PERSON><PERSON> thống hỗ trợ tạo và hiển thị các mã giảm giá.
4.1.2: <PERSON><PERSON> thống hỗ trợ tạo và hiển thị các chương trình khuyến mãi (ví dụ: mua <PERSON> tặng Y, gi<PERSON><PERSON> gi<PERSON> theo phần trăm).
4.1.3: <PERSON>ười dùng có thể áp dụng mã giảm giá hoặc tham gia chương trình khuyến mãi.

## Story 4.2 Xây dựng cơ chế thu thập và quản lý khách hàng tiềm năng

As a marketing manager, I want to capture potential customer information, so that I can build a lead database for future campaigns.

## Acceptance Criteria

4.2.1: Hệ thống hỗ trợ hiển thị pop-up thu thập email/số điện thoại.
4.2.2: Hệ thống hỗ trợ form liên hệ để người dùng gửi yêu cầu.
4.2.3: Dữ liệu khách hàng tiềm năng được lưu trữ và có thể truy xuất.

## Story 4.3 Tích hợp các kênh marketing cơ bản

As a marketing manager, I want to track user behavior and campaign performance, so that I can optimize marketing efforts.

## Acceptance Criteria

4.3.1: Tích hợp Google Analytics để theo dõi lưu lượng truy cập và hành vi người dùng.
4.3.2: Tích hợp Facebook Pixel để theo dõi chuyển đổi và chạy quảng cáo.
4.3.3: Tích hợp Zalo OA (Official Account) để tương tác với khách hàng.
4.3.4: Cấu hình cơ bản cho SEO và SEM.

## Story 4.4 Tối ưu hóa hiệu suất tải trang và Core Web Vitals

As a user, I want the website to load quickly, so that I have a smooth and enjoyable browsing experience.

## Acceptance Criteria

4.4.1: Tối ưu hóa hình ảnh (nén, định dạng phù hợp, lazy loading).
4.4.2: Tối ưu hóa mã nguồn (minification, code splitting).
4.4.3: Đảm bảo điểm Core Web Vitals (LCP, FID, CLS) đạt mức 'Good' trên Google PageSpeed Insights.
4.4.4: Tận dụng các tính năng tối ưu hóa của Next.js 13.
4.4.5: Tận dụng Vite cho quá trình build tối ưu.

## Story 4.5 Tối ưu hóa SEO On-page cơ bản

As a marketing manager, I want the website to rank well in search engines, so that I can attract organic traffic.

## Acceptance Criteria

4.5.1: Cấu trúc URL thân thiện với SEO.
4.5.2: Các trang có thẻ Meta Title và Description được tối ưu.
4.5.3: Nội dung chất lượng và từ khóa liên quan được sử dụng hợp lý.
4.5.4: Triển khai Schema Markup (Structured Data) cho sản phẩm và đánh giá.
4.5.5: Cấu hình Sitemap và Robots.txt.

## Story 4.6 Đảm bảo tối ưu hóa cho thiết bị di động và HTTPS

As a user, I want to access the website seamlessly on my mobile device, and feel secure while browsing.

## Acceptance Criteria

4.6.1: Website hiển thị và hoạt động tốt trên các thiết bị di động (Mobile-first Indexing).
4.6.2: Toàn bộ website được triển khai trên HTTPS.

# Epic 1 Nền tảng & <PERSON><PERSON> tầng cốt lõi

Epic này nhằm thiết lập nền tảng kỹ thuật vững chắc cho dự án, bao gồm việc cấu hình môi trường phát triển, thi<PERSON><PERSON> lập cấu trúc monorepo với pnpm, triển khai quy trình CI/CD cơ bản, và xây dựng các dịch vụ cốt lõi như xác thực người dùng và quản lý dữ liệu sản phẩm ban đầu, đả<PERSON> bảo một hệ thống ổn định và sẵn sàng cho các giai đoạn phát triển tiếp theo.

## Story 1.1 Thiết lập môi trường phát triển và Monorepo

As a developer, I want to set up the monorepo structure with pnpm, so that I can manage frontend and backend code efficiently.

## Acceptance Criteria

1.1.1: <PERSON><PERSON><PERSON> khởi tạo với pnpm.
1.1.2: Các project frontend (Next.js) và backend (Node.js/Express) được tạo trong monorepo.
1.1.3: Cấu hình TypeScript được thiết lập cho cả frontend và backend.
1.1.4: Các script cơ bản để chạy dev server và build được cấu hình.

## Story 1.2 Triển khai xác thực người dùng cơ bản với Supabase

As a user, I want to be able to register and log in to the platform, so that I can access personalized features.

## Acceptance Criteria

1.2.1: Tích hợp Supabase cho xác thực người dùng (đăng ký, đăng nhập, đăng xuất).
1.2.2: Người dùng có thể đăng ký tài khoản mới bằng email/mật khẩu.
1.2.3: Người dùng có thể đăng nhập và đăng xuất thành công.
1.2.4: Thông tin người dùng cơ bản được lưu trữ trong Supabase.

## Story 1.3 Quản lý dữ liệu sản phẩm ban đầu

As an administrator, I want to be able to add, view, update, and delete product information, so that I can manage the product catalog.

## Acceptance Criteria

1.3.1: Thiết lập schema database cho sản phẩm trong Supabase (tên, mô tả, giá, hình ảnh, số lượng, danh mục).
1.3.2: API backend (Node.js/Express) cho phép thêm, lấy, cập nhật, xóa sản phẩm.
1.3.3: Dữ liệu sản phẩm mẫu được nhập vào database.

## Story 1.4 Thiết lập CI/CD cơ bản với Netlify

As a developer, I want to have an automated CI/CD pipeline, so that I can build and deploy code changes automatically.

## Acceptance Criteria

1.4.1: Repository được kết nối với Netlify.
1.4.2: Cấu hình Netlify cho việc build và deploy frontend (Next.js).
1.4.3: Mỗi khi có commit mới vào nhánh chính, ứng dụng frontend được tự động deploy.
1.4.4: Backend API được deploy và có thể truy cập được.

# Epic List

*   **Epic 1: <PERSON><PERSON><PERSON> tảng & <PERSON><PERSON> tầng cốt lõi:** Thi<PERSON>t lập môi trường phát triển, c<PERSON><PERSON> tr<PERSON><PERSON> monorepo, CI/CD, và triển khai các dịch vụ cơ bản như xác thực người dùng và quản lý dữ liệu sản phẩm ban đầu.
*   **Epic 2: Trải nghiệm duyệt sản phẩm:** <PERSON><PERSON><PERSON> dựng các trang chủ, danh mục và chi tiết sản phẩm, bao gồm tìm kiếm, l<PERSON><PERSON>, hiển thị thông tin sản phẩm và đánh giá/bình luận.
*   **Epic 3: Quy trình mua hàng & Thanh toán:** Triển khai chức năng giỏ hàng, quy trình thanh toán đơn giản với nhiều phư<PERSON><PERSON> thức, và quản lý đơn hàng.
*   **Epic 4: Marketing & Tối ưu hiệu suất:** T<PERSON><PERSON> hợ<PERSON> các công cụ marketing và khuyến mãi cơ bản, đồng thời tối ưu hóa toàn diện hiệu suất tải trang và khả năng hiển thị trên công cụ tìm kiếm (SEO), đảm bảo nền tảng hoạt động nhanh chóng, mượt mà và dễ dàng được tìm thấy.

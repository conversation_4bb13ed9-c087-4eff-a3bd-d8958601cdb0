# Epic 3 Quy trình mua hàng & <PERSON>h toán

Epic này nhằm triển khai các chức năng cốt lõi cho phép người dùng hoàn tất quá trình mua hàng một cách suôn sẻ và an toàn, bao gồm quản lý giỏ hàng, quy trình thanh toán đơn giản với nhiều phươ<PERSON> thức, và theo dõi đơn hàng.

## Story 3.1 Xây dựng chức năng Giỏ hàng

As a user, I want to add products to a shopping cart and manage them, so that I can prepare for purchase.

## Acceptance Criteria

3.1.1: Người dùng có thể thêm sản phẩm vào giỏ hàng từ trang chi tiết sản phẩm.
3.1.2: Giỏ hàng hiển thị rõ ràng và trực quan các sản phẩm đã chọn (t<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, t<PERSON><PERSON> tiền).
3.1.3: Người dùng có thể cập nhật số lượng sản phẩm trong giỏ hàng.
3.1.4: <PERSON>ười dùng có thể xóa sản phẩm khỏi giỏ hàng.
3.1.5: Hệ thống hỗ trợ áp dụng mã giảm giá trong giỏ hàng.

## Story 3.2 Triển khai Quy trình thanh toán đơn giản (một trang)

As a user, I want to complete my purchase quickly and securely, so that I can receive my products.

## Acceptance Criteria

3.2.1: Quy trình thanh toán được thiết kế đơn giản, chỉ trong một trang.
3.2.2: Người dùng có thể nhập thông tin giao hàng (tên, địa chỉ, số điện thoại).
3.2.3: Người dùng có thể chọn phương thức vận chuyển.
3.2.4: Hệ thống hiển thị tổng số tiền cần thanh toán, bao gồm phí vận chuyển và giảm giá.

## Story 3.3 Tích hợp các phương thức thanh toán cơ bản

As a user, I want to pay using various common methods, so that I have flexibility in completing my purchase.

## Acceptance Criteria

3.3.1: Hỗ trợ thanh toán khi nhận hàng (COD).
3.3.2: Hỗ trợ thanh toán qua chuyển khoản ngân hàng.
3.3.3: Hỗ trợ thanh toán qua ví điện tử (ví dụ: Momo, ZaloPay).
3.3.4: Hỗ trợ thanh toán qua thẻ tín dụng/ghi nợ.

## Story 3.4 Xác nhận đơn hàng và Trang "Đặt hàng thành công"

As a user, I want to receive confirmation of my order, so that I know my purchase was successful.

## Acceptance Criteria

3.4.1: Sau khi thanh toán thành công, người dùng được chuyển hướng đến trang xác nhận đơn hàng.
3.4.2: Trang xác nhận đơn hàng hiển thị chi tiết đơn hàng và trạng thái.
3.4.3: Người dùng nhận được email xác nhận đơn hàng.

## Story 3.5 Đảm bảo bảo mật cho quy trình thanh toán

As a user, I want my payment information to be secure, so that I can trust the platform.

## Acceptance Criteria

3.5.1: Toàn bộ quy trình thanh toán được bảo vệ bằng HTTPS.
3.5.2: Dữ liệu thanh toán nhạy cảm được xử lý an toàn và tuân thủ các tiêu chuẩn bảo mật.

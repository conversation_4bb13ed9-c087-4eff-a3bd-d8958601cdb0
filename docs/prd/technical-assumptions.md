# Technical Assumptions

## Repository Structure: Monorepo

## Service Architecture

Kiến trúc dịch vụ sẽ dựa trên Node.js/Express cho API backend ch<PERSON>h, kết hợp với các Serverless Functions (Netlify Functions) cho các tác vụ nhẹ hoặc xử lý webhook. Điều này cho phép sự linh hoạt và khả năng mở rộng, đồng thời tận dụng tối đa các dịch vụ của Netlify.

## Testing Requirements

Chúng tôi sẽ áp dụng chiến lược kiểm thử toàn diện bao gồm:
*   **Kiểm thử đơn vị (Unit Testing):** Đảm bảo từng thành phần nhỏ của mã hoạt động đúng.
*   **Kiểm thử tích hợp (Integration Testing):** Xác minh sự tương tác giữa các module và dịch vụ khác <PERSON>hau (ví dụ: backend với database, frontend với API).
*   **Kiểm thử đầu cuối (End-to-End Testing):** <PERSON>ô phỏng hành vi người dùng thực để kiểm tra toàn bộ luồng ứng dụng.

## Additional Technical Assumptions and Requests

*   **Công cụ quản lý trạng thái Frontend:** Sẽ sử dụng TanStack Query để quản lý trạng thái server-side và caching dữ liệu.
*   **Quản lý gói:** Sử dụng pnpm để quản lý các gói phụ thuộc trong monorepo.
*   **Môi trường phát triển:** Cần có môi trường phát triển cục bộ dễ dàng thiết lập và đồng bộ với môi trường sản phẩm.
*   **CI/CD:** Thiết lập quy trình Tích hợp liên tục/Triển khai liên tục (CI/CD) tự động để đảm bảo chất lượng mã và triển khai nhanh chóng.
*   **Giám sát & Ghi log:** Triển khai hệ thống giám sát hiệu suất ứng dụng (APM) và ghi log tập trung để dễ dàng phát hiện và khắc phục lỗi.

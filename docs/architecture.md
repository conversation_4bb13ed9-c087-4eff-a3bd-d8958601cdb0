
# Phát triển nền tảng thương mại điện tử giày Fullstack Architecture Document

## Introduction

This document outlines the complete fullstack architecture for Phát triển nền tảng thương mại điện tử giày, including backend systems, frontend implementation, and their integration. It serves as the single source of truth for AI-driven development, ensuring consistency across the entire technology stack.

This unified approach combines what would traditionally be separate backend and frontend architecture documents, streamlining the development process for modern fullstack applications where these concerns are increasingly intertwined.

### Starter Template or Existing Project
N/A - Greenfield project

### Change Log
| Date | Version | Description | Author |
| :--- | :--- | :--- | :--- |
| 2025-08-19 | 1.0 | Initial draft | Winston |

---
## High Level Architecture

### Technical Summary
The overall architecture is designed according to the Jamstack model, leveraging Next.js 13 for the frontend and Serverless Functions (deployed on Netlify) for the backend API. This approach ensures maximum performance, flexible scalability, and an excellent developer experience (DX). The frontend will be built with React components using TypeScript, while the backend API will communicate with Supabase to manage data, authentication, and file storage. The monorepo structure using pnpm will help manage code uniformly, allowing logic and types to be shared between the frontend and backend, thereby meeting the project's goals of rapid development and easy maintenance.

### Platform and Infrastructure Choice
**Platform:** Vercel (for Frontend) & Netlify (for Backend Functions) combined with Supabase (for Backend-as-a-Service)
**Key Services:** Vercel: Hosting, Edge CDN, Analytics. Netlify: Functions (for API). Supabase: Database (Postgres), Auth, Storage.
**Deployment Host and Regions:** Global (Vercel/Netlify Edge), Southeast Asia (for Supabase if possible)

### Repository Structure
**Structure:** Monorepo
**Monorepo Tool:** pnpm workspaces
**Package Organization:** `apps` for deployable applications (web, api) and `packages` for shared code (UI, types, configs).

### High Level Architecture Diagram
```mermaid
graph TD
    subgraph "User"
        A[Browser/Client]
    end

    subgraph "Frontend (Vercel)"
        B[Next.js App] --> C[Static Assets on Edge CDN]
    end

    subgraph "Backend API (Netlify Functions)"
        D[API Gateway]
    end

    subgraph "Backend as a Service (Supabase)"
        E[Authentication]
        F[Database - PostgreSQL]
        G[File Storage]
    end
    
    subgraph "Third-party Services"
        H[Payment Gateway]
        I[Shipping Provider]
    end

    A --> B
    B --> D
    D --> F
    D --> E
    D --> G
    D --> H
    D --> I
```

### Architectural Patterns
- **Jamstack Architecture:** Building static sites (SSG) or server-side rendering (SSR) with Next.js and using APIs via Serverless Functions. _Rationale:_ Optimizes performance, security, and scalability.
- **Component-Based UI:** Building the user interface from independent and reusable React components. _Rationale:_ Increases maintainability, code reuse, and allows for parallel development.
- **API Gateway Pattern:** Using Netlify Functions as a single API gateway to communicate with backend services (Supabase) and third parties. _Rationale:_ Centralizes authentication, logging, and endpoint management, simplifying the client side.
- **Repository Pattern (for Backend):** Abstracting the data access layer, separating business logic from direct database queries. _Rationale:_ Facilitates easier testing and flexibility in changing or expanding the database in the future.

---
## Tech Stack

### Technology Stack Table
| Category | Technology | Version | Purpose | Rationale |
| :--- | :--- | :--- | :--- | :--- |
| Frontend Language | TypeScript | ~5.4.5 | Type safety, modern JS features | Industry standard for robust frontend development, reduces errors. |
| Frontend Framework | Next.js | ~13.5 | Fullstack React framework with SSR, SSG | A stable, mature, and widely-used version for production applications. |
| UI Component Library | shadcn/ui | latest | Foundational, unstyled accessible components | Provides full ownership of component code, highly customizable. |
| State Management | TanStack Query | ~5.51.1 | Server state management, caching, refetching | Simplifies data fetching, caching, and synchronization. |
| Backend Language | TypeScript | ~5.4.5 | Type safety, code sharing with frontend | Consistency across the stack, improved maintainability. |
| Backend Framework | Node.js / Express | ~4.19.2 | Lightweight, flexible backend framework | Mature, large ecosystem, easy to build REST APIs. |
| API Style | REST | - | Standard for client-server communication | Well-understood, broad support, good for CRUD tasks. |
| Database | Supabase (PostgreSQL) | latest | Open source Firebase alternative, relational DB | Provides auth, storage, and a powerful Postgres DB. |
| Cache | Redis | latest | In-memory data store for caching sessions, etc. | High performance, versatile for various caching needs. |
| File Storage | Supabase Storage | latest | S3-compatible object storage | Integrated with Supabase, easy to manage files. |
| Authentication | Supabase Auth | latest | Handles user auth, JWTs, social logins | Secure, easy to integrate, feature-rich. |
| Frontend Testing | Jest, React Testing Library | latest | Unit and component testing | Standard for React, focuses on user behavior. |
| Backend Testing | Jest, Supertest | latest | Unit and API endpoint testing | Fast, easy to mock, good for testing Express APIs. |
| E2E Testing | Playwright | latest | End-to-end browser automation | Reliable, fast, supports multiple browsers. |
| Build Tool / Bundler | Webpack | latest | Module bundler for JavaScript | Default, stable, and powerful bundler integrated with Next.js 13. |
| IaC Tool | Terraform | latest | Infrastructure as Code for cloud resources | Declarative, multi-platform, manages infrastructure state. |
| CI/CD | GitHub Actions | - | Automated build, test, and deployment | Natively integrated with GitHub, highly configurable. |
| Monitoring | Vercel Analytics, Sentry | latest | Performance monitoring and error tracking | Easy to set up, provides valuable insights. |
| Logging | Netlify Logs, Sentry | latest | Centralized logging for backend and frontend | Essential for debugging and monitoring application health. |
| CSS Framework | Tailwind CSS | latest | Utility-first CSS framework | Rapid styling, highly customizable, no unused CSS. |

---
## Data Models

#### 1. User (Người dùng)
*   **Mục đích:** Đại diện cho một khách hàng đã đăng ký tài khoản trên hệ thống.
*   **TypeScript Interface:**
    ```typescript
    interface User {
      id: string;
      email: string;
      full_name?: string;
      avatar_url?: string;
      created_at: string;
    }
    ```
*   **Mối quan hệ:** Một `User` có thể có nhiều `Order` và `Review`.

#### 2. Product (Sản phẩm)
*   **Mục đích:** Đại diện cho một mặt hàng giày được bán trên trang web.
*   **TypeScript Interface:**
    ```typescript
    interface Product {
      id: string;
      name: string;
      description: string;
      price: number;
      image_urls: string[];
      brand: string;
      category: string;
      inventory_count: number;
      created_at: string;
    }
    ```
*   **Mối quan hệ:** Một `Product` có thể có nhiều `Review` và nằm trong nhiều `OrderItem`.

#### 3. Order (Đơn hàng)
*   **Mục đích:** Đại diện cho một giao dịch mua hàng đã được hoàn tất.
*   **TypeScript Interface:**
    ```typescript
    interface Order {
      id: string;
      user_id: string;
      total_amount: number;
      status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
      shipping_address: {
        recipient_name: string;
        phone: string;
        address: string;
        city: string;
      };
      payment_method: string;
      created_at: string;
      order_items?: OrderItem[];
    }
    ```
*   **Mối quan hệ:** Một `Order` thuộc về một `User` và có nhiều `OrderItem`.

#### 4. OrderItem (Mục trong đơn hàng)
*   **Mục đích:** Một dòng sản phẩm cụ thể trong một đơn hàng.
*   **TypeScript Interface:**
    ```typescript
    interface OrderItem {
      id: string;
      order_id: string;
      product_id: string;
      quantity: number;
      price_at_purchase: number;
    }
    ```
*   **Mối quan hệ:** Thuộc về một `Order` và liên quan đến một `Product`.

#### 5. Review (Đánh giá)
*   **Mục đích:** Lưu trữ đánh giá của người dùng về một sản phẩm.
*   **TypeScript Interface:**
    ```typescript
    interface Review {
      id: string;
      user_id: string;
      product_id: string;
      rating: number;
      comment?: string;
      created_at: string;
      user?: User;
    }
    ```
*   **Mối quan hệ:** Thuộc về một `User` và một `Product`.

---
## API Specification

### REST API Specification (OpenAPI 3.0)
```yaml
openapi: 3.0.1
info:
  title: "Shoe Store API"
  description: "API chính thức cho nền tảng thương mại điện tử giày."
  version: "1.0.0"
servers:
  - url: "/api"
    description: "Server API chính"
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
paths:
  /products:
    get:
      summary: "Lấy danh sách sản phẩm"
      # ...
  /products/{productId}:
    get:
      summary: "Lấy chi tiết một sản phẩm"
      # ...
  /orders:
    post:
      summary: "Tạo một đơn hàng mới"
      # ...
```

---
## Components

#### 1. Frontend Application
*   **Trách nhiệm:** Hiển thị UI, xử lý tương tác người dùng, giao tiếp với API Service.
*   **Ngăn xếp công nghệ:** Next.js, React, TypeScript, TanStack Query, shadcn/ui.

#### 2. API Service
*   **Trách nhiệm:** Xử lý logic nghiệp vụ, nhận request từ Frontend, giao tiếp với các dịch vụ backend khác.
*   **Ngăn xếp công nghệ:** Node.js, Express, TypeScript (trên Netlify Functions).

#### 3. Auth Service
*   **Trách nhiệm:** Quản lý vòng đời người dùng (đăng ký, đăng nhập...).
*   **Ngăn xếp công nghệ:** Supabase Auth.

#### 4. Database Service
*   **Trách nhiệm:** Lưu trữ và truy xuất dữ liệu có cấu trúc.
*   **Ngăn xếp công nghệ:** Supabase (PostgreSQL).

#### 5. Storage Service
*   **Trách nhiệm:** Lưu trữ file media.
*   **Ngăn xếp công nghệ:** Supabase Storage.

#### 6. Payment Service
*   **Trách nhiệm:** Xử lý giao dịch thanh toán an toàn.
*   **Ngăn xếp công nghệ:** API của bên thứ ba (ví dụ: Stripe).

---
## External APIs

#### 1. Payment Gateway (Ví dụ: Stripe)
*   **Mục đích:** Xử lý thanh toán kỹ thuật số an toàn.
*   **Tài liệu:** `https://stripe.com/docs/api`

#### 2. Shipping Provider (Ví dụ: Giao Hàng Nhanh)
*   **Mục đích:** Tính phí vận chuyển, tạo đơn hàng vận chuyển.
*   **Tài liệu:** `https://api.ghn.vn/home/<USER>/`

---
## Core Workflows

#### User Checkout Sequence Diagram
```mermaid
sequenceDiagram
    actor User
    participant Frontend as Frontend App
    participant Backend as API Service
    participant Stripe as Payment Service
    participant DB as Database Service

    User->>Frontend: Bấm nút "Thanh toán"
    Frontend->>Backend: POST /api/orders/initiate
    activate Backend
    Backend->>Stripe: POST /v1/payment_intents
    activate Stripe
    Stripe-->>Backend: Trả về client_secret
    deactivate Stripe
    Backend->>DB: Tạo đơn hàng "pending"
    Backend-->>Frontend: Trả về client_secret
    deactivate Backend
    User->>Stripe: Nhập thông tin thẻ
    Stripe-->>User: Hiển thị kết quả
    Stripe->>Backend: POST /api/webhooks/stripe
    activate Backend
    Backend->>DB: Cập nhật trạng thái đơn hàng
    deactivate Backend
```

---
## Database Schema
```sql
-- Bảng lưu thông tin công khai của người dùng
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  avatar_url TEXT
);

-- Bảng sản phẩm
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  price NUMERIC(10, 2) NOT NULL,
  -- ...
);

-- ... các bảng khác
```

---
## Frontend Architecture

#### Component Organization
```text
src/
├── app/
│   ├── products/
│   │   ├── _components/
│   │   └── [id]/
│   │       └── _components/
├── components/
│   ├── ui/
│   └── common/
└── lib/
```

#### State Management
*   **Server State:** TanStack Query
*   **Global Client State:** React Context / Zustand
*   **Local State:** useState / useReducer

---
## Backend Architecture

#### Service Architecture
*   **Mô hình:** Serverless, file-based routing trên Netlify Functions.

#### Data Access Layer
*   **Pattern:** Repository Pattern để tách biệt logic truy vấn CSDL.

#### Authentication
*   **Luồng:** JWT do Supabase cung cấp, xác thực ở middleware.

---
## Unified Project Structure
```plaintext
shoe-page-kokonutui/
├── apps/
│   ├── web/
│   └── api/
├── packages/
│   ├── ui/
│   ├── shared-types/
│   └── config/
├── docs/
└── pnpm-workspace.yaml
```

---
## Development Workflow

*   **Công cụ:** Node.js v20+, pnpm, Netlify CLI
*   **Quy trình Git:** Feature Branch Workflow với Pull Requests và Code Review.

---
## Deployment Architecture

*   **Frontend:** Vercel, tự động deploy từ nhánh `main`.
*   **Backend:** Netlify, tự động deploy từ nhánh `main`.
*   **Environments:** Development (local), Staging/Preview (tự động cho mỗi PR), Production.

---
## Security and Performance

*   **Bảo mật:** CSP, `httpOnly` cookies cho JWT, input validation với `zod`, Rate Limiting, CORS.
*   **Hiệu suất:** Tối ưu bundle size, Vercel/Edge Caching, TanStack Query, tối ưu CSDL với indexes, Redis cache.

---
## Testing Strategy

*   **Mô hình:** Kim tự tháp kiểm thử (Unit, Integration, E2E).
*   **Công cụ:** Jest, React Testing Library, Supertest, Playwright.
*   **Tổ chức:** Test được đặt cùng với code (co-location), E2E test ở thư mục riêng.

---
## Coding Standards

*   **Quy tắc chính:** Chia sẻ Types, dùng lớp Service, không truy cập `process.env` trực tiếp, dùng Repository Pattern, co-location cho components.
*   **Quy ước đặt tên:** PascalCase cho Components, `use` + camelCase cho Hooks, `snake_case` cho bảng CSDL.

---
## Error Handling Strategy

*   **Luồng:** Lỗi được bắt và định dạng ở backend, trả về JSON lỗi chuẩn. Frontend hiển thị thông báo thân thiện và log lỗi chi tiết lên Sentry.
*   **Định dạng:** `ApiError` interface chuẩn.

---
## Monitoring and Observability

*   **Công cụ:** Vercel Analytics, Netlify Analytics, Sentry.
*   **Chỉ số chính:** Core Web Vitals, JS Error Rate, Function Duration, API Error Rate.

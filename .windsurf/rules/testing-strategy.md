---
trigger: always_on
description: <PERSON>ể<PERSON> tra tuân thủ chiến lược testing
---

# Quy tắc Testing

## Mô hình Testing

- Tuân thủ mô hình kim tự tháp kiểm thử:
  - Unit Tests: <PERSON><PERSON><PERSON><PERSON> nh<PERSON>t, kiểm tra các đơn vị nhỏ của code
  - Integration Tests: Kiểm tra tương tác giữa các thành phần
  - E2E Tests: Ít nhất, kiểm tra luồng hoạt động của toàn bộ ứng dụng

## Công cụ Testing

- Frontend:
  - Jest: Test runner
  - React Testing Library: Testing React components
- Backend:
  - Jest: Test runner
  - Supertest: Testing API endpoints
- E2E:
  - Playwright: Testing toàn bộ ứng dụng

## Tổ chức Tests

- Co-location: Tests nên được đặt gần với code được test
  - Unit và component tests: Đặt trong cùng thư mục với code được test với tên file `*.test.ts(x)` hoặc `*.spec.ts(x)`
  - E2E tests: Đặt trong thư mục riêng `e2e/` ở root của project

## Quy tắc viết Tests

- Mỗi test chỉ nên kiểm tra một chức năng
- Sử dụng mocks cho các dependency bên ngoài
- Đặt tên test rõ ràng, mô tả chức năng được test
- Sử dụng AAA pattern (Arrange-Act-Assert)
- Coverage tối thiểu: 70% cho code mới

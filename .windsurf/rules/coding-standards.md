---
trigger: always_on
description: Ki<PERSON>m tra tuân thủ coding standards
---

# Quy tắc Coding Standards

## Quy tắc chung

- Chia sẻ Types giữa frontend và backend
- Sử dụng lớp Service cho logic nghiệp vụ
- Không truy cập `process.env` trự<PERSON> ti<PERSON>, sử dụng các hàm wrapper
- Sử dụng Repository Pattern cho truy cập dữ liệu
- Co-location cho components (đặt components gần với nơi sử dụng)

## Quy ước đặt tên

- PascalCase cho Components (ví dụ: `UserProfile`)
- `use` + camelCase cho Hooks (ví dụ: `useAuth`)
- `snake_case` cho bảng CSDL (ví dụ: `user_profiles`)
- camelCase cho biến và hàm
- PascalCase cho interfaces và types

## Cấu trúc file

- Imports phải được sắp xếp: React/Next.js, thư viện bên thứ ba, components, hooks, utils
- Mỗi file chỉ nên export một component chính
- Các component nhỏ có thể được định nghĩa trong cùng file với component chính nếu chúng chỉ được sử dụng bởi component chính

## Kiểu dữ liệu

- Luôn sử dụng TypeScript với kiểu dữ liệu rõ ràng
- Tránh sử dụng `any` hoặc `unknown` khi có thể
- Sử dụng interfaces cho objects và types cho unions/intersections

## Xử lý lỗi

- Sử dụng try/catch cho các hoạt động bất đồng bộ
- Trả về thông báo lỗi có ý nghĩa
- Không hiển thị thông tin nhạy cảm trong thông báo lỗi

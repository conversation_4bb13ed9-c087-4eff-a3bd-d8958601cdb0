---
trigger: always_on
description: <PERSON><PERSON><PERSON> tra tuân thủ cấu trúc dự án
---

# Quy tắc Cấu trúc Dự án

## Cấu trúc Monorepo

- Sử dụng pnpm workspace cho quản lý monorepo
- Các ứng dụng được đặt trong thư mục `apps/`
- Các package dùng chung được đặt trong thư mục `packages/`

## Frontend (Next.js)

- Đường dẫn: `apps/web`
- Cấu trúc thư mục:
  - `app/`: Các trang và route theo App Router của Next.js 13
  - `components/`: Các component dùng chung
    - `ui/`: Các component UI cơ bản
    - `common/`: Các component phức tạp hơn
  - `lib/`: Các utility và service
  - `__tests__/`: Các file test

## Backend (Node.js/Express)

- Đường dẫn: `apps/api`
- Cấu trúc thư mục:
  - `src/`: Mã nguồn chính
    - `controllers/`: <PERSON><PERSON> lý request và response
    - `services/`: Logic nghiệp vụ
    - `repositories/`: T<PERSON>y cập dữ liệu
    - `middlewares/`: Middleware Express
    - `routes/`: Định nghĩa routes
  - `__tests__/`: Các file test

## Shared Packages

- `packages/shared-types`: Types dùng chung giữa frontend và backend
- `packages/ui`: Các component UI dùng chung
- `packages/config`: Cấu hình dùng chung (ESLint, TypeScript)

## Co-location

- Các component chỉ dùng cho một trang cụ thể nên được đặt trong thư mục `_components` của trang đó
- Các component dùng chung nên được đặt trong thư mục `components`

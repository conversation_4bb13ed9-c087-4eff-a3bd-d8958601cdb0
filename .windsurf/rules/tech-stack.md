---
trigger: always_on
description: <PERSON><PERSON><PERSON> tra tuân thủ tech stack
---

# Quy tắc Tech Stack

## Frontend

- **Ngôn ngữ**: TypeScript ~5.4.5
- **Framework**: Next.js ~13.5
- **UI Component Library**: shadcn/ui
- **State Management**:
  - Server State: TanStack Query ~5.51.1
  - Global Client State: React Context / Zustand
  - Local State: useState / useReducer
- **CSS Framework**: Tailwind CSS
- **Build Tool**: Webpack (mặc định của Next.js 13)

## Backend

- **Ngôn ngữ**: TypeScript ~5.4.5
- **Framework**: Node.js / Express ~4.19.2
- **API Style**: REST
- **Database**: Supabase (PostgreSQL)
- **Cache**: Redis
- **File Storage**: Supabase Storage
- **Authentication**: Supabase Auth

## Testing

- **Frontend Testing**: Jest, React Testing Library
- **Backend Testing**: Jest, Supertest
- **E2E Testing**: Playwright

## DevOps

- **Package Manager**: pnpm
- **IaC Tool**: Terraform
- **CI/CD**: GitHub Actions
- **Monitoring**: Vercel Analytics, Sentry
- **Logging**: Netlify Logs, Sentry

## Phiên bản

- Các phiên bản phải được chỉ định rõ ràng trong package.json
- Không sử dụng phiên bản với ký hiệu ^ (caret) để tránh các vấn đề về tương thích
- Các dependency chính phải tuân thủ phiên bản được chỉ định trong tech stack

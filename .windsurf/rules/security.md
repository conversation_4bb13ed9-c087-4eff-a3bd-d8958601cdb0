---
trigger: always_on
description: <PERSON><PERSON><PERSON> tra tuân thủ quy tắc bảo mật
---

# Quy tắc Bảo mật

## Quản lý Biến Môi trường

- Không commit trực tiếp các file `.env` vào repository
- Sử dụng `.env.example` để hướng dẫn thiết lập biến môi trường
- Không truy cập `process.env` trực tiế<PERSON>, sử dụng các hàm wrapper
- Prefix các biến môi trường frontend với `NEXT_PUBLIC_` nếu cần sử dụng ở client-side

## Xác thực và Phân quyền

- Sử dụng Supabase Auth cho xác thực người dùng
- Kiểm tra JWT token cho mọi API endpoint cần xác thực
- Thực hiện kiểm tra phân quyền ở cả client và server
- Không lưu thông tin nhạy cảm trong localStorage hoặc sessionStorage

## Xử lý Dữ liệu

- Validate tất cả dữ liệu đầu vào từ người dùng
- Sử dụng prepared statements hoặc ORM để tránh SQL injection
- Không hiển thị thông báo lỗi chi tiết cho người dùng
- Mã hóa dữ liệu nhạy cảm trước khi lưu trữ

## Frontend Security

- Sử dụng Content Security Policy (CSP)
- Tránh sử dụng `dangerouslySetInnerHTML` hoặc `eval`
- Sử dụng HTTPS cho tất cả các request
- Implement CSRF protection cho các form

## API Security

- Rate limiting cho các API endpoint
- Sử dụng CORS đúng cách
- Không expose thông tin nhạy cảm trong response
- Validate và sanitize tất cả các tham số query và body

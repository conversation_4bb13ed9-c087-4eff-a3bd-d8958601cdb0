/**
 * <PERSON><PERSON><PERSON> kiểm tra cho trang đăng nhập
 * Kiểm tra hiển thị form, x<PERSON><PERSON> thực đầu vào và xử lý đăng nhập
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useRouter } from 'next/navigation'
import LoginPage from '@/app/login/page'
import { useAuth } from '@/lib/auth/auth-context'

// Mock cho useRouter và useAuth
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}))

jest.mock('@/lib/auth/auth-context', () => ({
  useAuth: jest.fn()
}))

describe('LoginPage', () => {
  const mockPush = jest.fn()
  const mockSignIn = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Thiết lập mock cho useRouter
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    })
    
    // Thiết lập mock cho useAuth
    ;(useAuth as jest.Mock).mockReturnValue({
      signIn: mockSignIn,
      user: null
    })
  })
  
  test('hiển thị form đăng nhập với các trường cần thiết', () => {
    render(<LoginPage />)
    
    // Kiểm tra tiêu đề trang
    expect(screen.getByRole('heading', { name: /đăng nhập/i })).toBeInTheDocument()
    
    // Kiểm tra các trường nhập liệu
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/mật khẩu/i)).toBeInTheDocument()
    
    // Kiểm tra nút đăng nhập
    expect(screen.getByRole('button', { name: /đăng nhập/i })).toBeInTheDocument()
    
    // Kiểm tra liên kết đến trang đăng ký
    expect(screen.getByText(/chưa có tài khoản/i)).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /đăng ký/i })).toBeInTheDocument()
  })
  
  test('hiển thị lỗi khi nhập liệu không hợp lệ', async () => {
    render(<LoginPage />)
    
    // Nhấn nút đăng nhập mà không nhập gì
    fireEvent.click(screen.getByRole('button', { name: /đăng nhập/i }))
    
    // Kiểm tra hiển thị thông báo lỗi
    await waitFor(() => {
      expect(screen.getByText(/email là bắt buộc/i)).toBeInTheDocument()
      expect(screen.getByText(/mật khẩu là bắt buộc/i)).toBeInTheDocument()
    })
    
    // Nhập email không hợp lệ
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: 'invalid-email' }
    })
    
    // Nhấn nút đăng nhập
    fireEvent.click(screen.getByRole('button', { name: /đăng nhập/i }))
    
    // Kiểm tra hiển thị thông báo lỗi
    await waitFor(() => {
      expect(screen.getByText(/email không hợp lệ/i)).toBeInTheDocument()
    })
  })
  
  test('gọi signIn khi form hợp lệ và chuyển hướng khi thành công', async () => {
    // Mock signIn thành công
    mockSignIn.mockResolvedValue({
      user: { id: '123', email: '<EMAIL>' },
      error: null
    })
    
    render(<LoginPage />)
    
    // Nhập thông tin hợp lệ
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    fireEvent.change(screen.getByLabelText(/mật khẩu/i), {
      target: { value: 'password123' }
    })
    
    // Nhấn nút đăng nhập
    fireEvent.click(screen.getByRole('button', { name: /đăng nhập/i }))
    
    // Kiểm tra signIn được gọi với đúng tham số
    await waitFor(() => {
      expect(mockSignIn).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
    
    // Kiểm tra chuyển hướng sau khi đăng nhập thành công
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/')
    })
  })
  
  test('hiển thị lỗi khi đăng nhập thất bại', async () => {
    // Mock signIn thất bại
    mockSignIn.mockResolvedValue({
      user: null,
      error: 'Email hoặc mật khẩu không chính xác'
    })
    
    render(<LoginPage />)
    
    // Nhập thông tin
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    fireEvent.change(screen.getByLabelText(/mật khẩu/i), {
      target: { value: 'wrong-password' }
    })
    
    // Nhấn nút đăng nhập
    fireEvent.click(screen.getByRole('button', { name: /đăng nhập/i }))
    
    // Kiểm tra hiển thị thông báo lỗi
    await waitFor(() => {
      expect(screen.getByText(/email hoặc mật khẩu không chính xác/i)).toBeInTheDocument()
    })
  })
})

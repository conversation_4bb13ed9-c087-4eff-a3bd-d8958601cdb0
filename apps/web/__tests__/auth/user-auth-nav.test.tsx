/**
 * <PERSON>ài kiểm tra cho component UserAuthNav
 * Kiểm tra hiển thị các trạng thái khác nhau dựa trên trạng thái đăng nhập
 */
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import UserAuthNav from '@/components/auth/user-auth-nav'
import { useAuth } from '@/lib/auth/auth-context'

// Mock cho useAuth hook
jest.mock('@/lib/auth/auth-context', () => ({
  useAuth: jest.fn()
}))

describe('UserAuthNav Component', () => {
  const mockSignOut = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  test('hiển thị trạng thái loading khi isLoading là true', () => {
    // Thiết lập mock cho useAuth
    (useAuth as jest.Mock).mockReturnValue({
      user: null,
      isLoading: true,
      signOut: mockSignOut
    })
    
    render(<UserAuthNav />)
    
    // Kiểm tra hiển thị loading skeleton
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument()
  })
  
  test('hiển thị liên kết đăng nhập/đăng ký khi chưa đăng nhập', () => {
    // Thiết lập mock cho useAuth
    (useAuth as jest.Mock).mockReturnValue({
      user: null,
      isLoading: false,
      signOut: mockSignOut
    })
    
    render(<UserAuthNav />)
    
    // Kiểm tra hiển thị liên kết đăng nhập/đăng ký
    expect(screen.getByText('Đăng nhập')).toBeInTheDocument()
    expect(screen.getByText('Đăng ký')).toBeInTheDocument()
  })
  
  test('hiển thị thông tin người dùng và nút đăng xuất khi đã đăng nhập', () => {
    // Thiết lập mock cho useAuth với user đã đăng nhập
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '123',
        email: '<EMAIL>',
        full_name: 'Test User',
        created_at: '2023-01-01'
      },
      isLoading: false,
      signOut: mockSignOut
    })
    
    render(<UserAuthNav />)
    
    // Kiểm tra hiển thị tên người dùng
    expect(screen.getByText('Test User')).toBeInTheDocument()
    
    // Mở dropdown menu
    fireEvent.click(screen.getByRole('button'))
    
    // Kiểm tra hiển thị nút đăng xuất
    expect(screen.getByText('Đăng xuất')).toBeInTheDocument()
    
    // Kiểm tra chức năng đăng xuất
    fireEvent.click(screen.getByText('Đăng xuất'))
    expect(mockSignOut).toHaveBeenCalledTimes(1)
  })
  
  test('hiển thị email khi không có full_name', () => {
    // Thiết lập mock cho useAuth với user không có full_name
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '123',
        email: '<EMAIL>',
        created_at: '2023-01-01'
      },
      isLoading: false,
      signOut: mockSignOut
    })
    
    render(<UserAuthNav />)
    
    // Kiểm tra hiển thị email thay vì tên
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })
})

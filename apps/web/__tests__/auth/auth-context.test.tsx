/**
 * <PERSON><PERSON><PERSON> kiểm tra cho AuthContext
 * <PERSON><PERSON><PERSON> tra các chức năng xác thực: đăng ký, đăng nhập, đăng xuất và làm mới thông tin người dùng
 */
import { render, screen, act, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { AuthProvider, useAuth } from '@/lib/auth/auth-context'
import { createClient } from '@/lib/supabase/client'

// Mock cho Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn()
}))

// Component kiểm tra để sử dụng hook useAuth
const TestComponent = ({ testFunction }: { testFunction: () => void }) => {
  const auth = useAuth()
  // Gọi hàm kiểm tra và truyền đối tượng auth
  testFunction()
  return <div>Test Component</div>
}

describe('AuthContext', () => {
  // Mock cho các phương thức của Supabase
  const mockSignUp = jest.fn()
  const mockSignIn = jest.fn()
  const mockSignOut = jest.fn()
  const mockGetUser = jest.fn()
  const mockOnAuthStateChange = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Thiết lập mock cho Supabase client
    const mockSupabase = {
      auth: {
        signUp: mockSignUp,
        signInWithPassword: mockSignIn,
        signOut: mockSignOut,
        getUser: mockGetUser,
        onAuthStateChange: mockOnAuthStateChange
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({ data: {}, error: null })
          })
        })
      })
    }
    
    // Trả về mock client khi gọi createClient
    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)
    
    // Mock cho onAuthStateChange để trả về đối tượng subscription
    mockOnAuthStateChange.mockResolvedValue({
      data: {
        subscription: {
          unsubscribe: jest.fn()
        }
      }
    })
  })
  
  test('signUp gọi Supabase signUp và tạo profile', async () => {
    // Mock cho signUp thành công
    mockSignUp.mockResolvedValue({
      data: {
        user: { id: '123', email: '<EMAIL>' }
      },
      error: null
    })
    
    // Mock cho getUser để refreshUser hoạt động
    mockGetUser.mockResolvedValue({
      data: {
        user: { id: '123', email: '<EMAIL>' }
      },
      error: null
    })
    
    let authContext: any
    
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent testFunction={() => {
            authContext = useAuth()
          }} />
        </AuthProvider>
      )
    })
    
    // Gọi hàm signUp
    await act(async () => {
      const result = await authContext.signUp({
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'Test User'
      })
      
      // Kiểm tra kết quả
      expect(result.user).not.toBeNull()
      expect(result.error).toBeNull()
    })
    
    // Kiểm tra Supabase signUp được gọi với đúng tham số
    expect(mockSignUp).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
      options: {
        data: {
          full_name: 'Test User'
        }
      }
    })
  })
  
  test('signIn gọi Supabase signInWithPassword', async () => {
    // Mock cho signIn thành công
    mockSignIn.mockResolvedValue({
      data: {
        user: { id: '123', email: '<EMAIL>' }
      },
      error: null
    })
    
    // Mock cho getUser để refreshUser hoạt động
    mockGetUser.mockResolvedValue({
      data: {
        user: { id: '123', email: '<EMAIL>' }
      },
      error: null
    })
    
    let authContext: any
    
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent testFunction={() => {
            authContext = useAuth()
          }} />
        </AuthProvider>
      )
    })
    
    // Gọi hàm signIn
    await act(async () => {
      const result = await authContext.signIn({
        email: '<EMAIL>',
        password: 'password123'
      })
      
      // Kiểm tra kết quả
      expect(result.user).not.toBeNull()
      expect(result.error).toBeNull()
    })
    
    // Kiểm tra Supabase signInWithPassword được gọi với đúng tham số
    expect(mockSignIn).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })
  
  test('signOut gọi Supabase signOut', async () => {
    // Mock cho signOut thành công
    mockSignOut.mockResolvedValue({ error: null })
    
    let authContext: any
    
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent testFunction={() => {
            authContext = useAuth()
          }} />
        </AuthProvider>
      )
    })
    
    // Gọi hàm signOut
    await act(async () => {
      await authContext.signOut()
    })
    
    // Kiểm tra Supabase signOut được gọi
    expect(mockSignOut).toHaveBeenCalled()
  })
  
  test('refreshUser cập nhật trạng thái user', async () => {
    // Mock cho getUser thành công
    mockGetUser.mockResolvedValue({
      data: {
        user: {
          id: '123',
          email: '<EMAIL>',
          user_metadata: {
            full_name: 'Test User'
          }
        }
      },
      error: null
    })
    
    let authContext: any
    
    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent testFunction={() => {
            authContext = useAuth()
          }} />
        </AuthProvider>
      )
    })
    
    // Gọi hàm refreshUser
    await act(async () => {
      await authContext.refreshUser()
    })
    
    // Kiểm tra getUser được gọi
    expect(mockGetUser).toHaveBeenCalled()
    
    // Kiểm tra trạng thái user được cập nhật
    expect(authContext.user).not.toBeNull()
    expect(authContext.user.email).toBe('<EMAIL>')
  })
})

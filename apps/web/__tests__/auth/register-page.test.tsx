/**
 * <PERSON><PERSON><PERSON> kiểm tra cho trang đăng ký
 * Kiểm tra hiển thị form, xá<PERSON> thực đầu vào và xử lý đăng ký
 */
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useRouter } from 'next/navigation'
import RegisterPage from '@/app/register/page'
import { useAuth } from '@/lib/auth/auth-context'

// Mock cho useRouter và useAuth
jest.mock('next/navigation', () => ({
  useRouter: jest.fn()
}))

jest.mock('@/lib/auth/auth-context', () => ({
  useAuth: jest.fn()
}))

describe('RegisterPage', () => {
  const mockPush = jest.fn()
  const mockSignUp = jest.fn()
  
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Thiết lập mock cho useRouter
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush
    })
    
    // Thiết lập mock cho useAuth
    ;(useAuth as jest.Mock).mockReturnValue({
      signUp: mockSignUp,
      user: null
    })
  })
  
  test('hiển thị form đăng ký với các trường cần thiết', () => {
    render(<RegisterPage />)
    
    // Kiểm tra tiêu đề trang
    expect(screen.getByRole('heading', { name: /đăng ký/i })).toBeInTheDocument()
    
    // Kiểm tra các trường nhập liệu
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/mật khẩu/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/xác nhận mật khẩu/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/tên đầy đủ/i)).toBeInTheDocument()
    
    // Kiểm tra nút đăng ký
    expect(screen.getByRole('button', { name: /đăng ký/i })).toBeInTheDocument()
    
    // Kiểm tra liên kết đến trang đăng nhập
    expect(screen.getByText(/đã có tài khoản/i)).toBeInTheDocument()
    expect(screen.getByRole('link', { name: /đăng nhập/i })).toBeInTheDocument()
  })
  
  test('hiển thị lỗi khi nhập liệu không hợp lệ', async () => {
    render(<RegisterPage />)
    
    // Nhấn nút đăng ký mà không nhập gì
    fireEvent.click(screen.getByRole('button', { name: /đăng ký/i }))
    
    // Kiểm tra hiển thị thông báo lỗi
    await waitFor(() => {
      expect(screen.getByText(/email là bắt buộc/i)).toBeInTheDocument()
      expect(screen.getByText(/mật khẩu là bắt buộc/i)).toBeInTheDocument()
    })
    
    // Nhập email không hợp lệ
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: 'invalid-email' }
    })
    
    // Nhập mật khẩu quá ngắn
    fireEvent.change(screen.getByLabelText(/mật khẩu/i), {
      target: { value: '123' }
    })
    
    // Nhập xác nhận mật khẩu không khớp
    fireEvent.change(screen.getByLabelText(/xác nhận mật khẩu/i), {
      target: { value: '1234' }
    })
    
    // Nhấn nút đăng ký
    fireEvent.click(screen.getByRole('button', { name: /đăng ký/i }))
    
    // Kiểm tra hiển thị thông báo lỗi
    await waitFor(() => {
      expect(screen.getByText(/email không hợp lệ/i)).toBeInTheDocument()
      expect(screen.getByText(/mật khẩu phải có ít nhất 6 ký tự/i)).toBeInTheDocument()
      expect(screen.getByText(/mật khẩu không khớp/i)).toBeInTheDocument()
    })
  })
  
  test('gọi signUp khi form hợp lệ và chuyển hướng khi thành công', async () => {
    // Mock signUp thành công
    mockSignUp.mockResolvedValue({
      user: { id: '123', email: '<EMAIL>' },
      error: null
    })
    
    render(<RegisterPage />)
    
    // Nhập thông tin hợp lệ
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    fireEvent.change(screen.getByLabelText(/mật khẩu/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.change(screen.getByLabelText(/xác nhận mật khẩu/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.change(screen.getByLabelText(/tên đầy đủ/i), {
      target: { value: 'Test User' }
    })
    
    // Nhấn nút đăng ký
    fireEvent.click(screen.getByRole('button', { name: /đăng ký/i }))
    
    // Kiểm tra signUp được gọi với đúng tham số
    await waitFor(() => {
      expect(mockSignUp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
        full_name: 'Test User'
      })
    })
    
    // Kiểm tra chuyển hướng sau khi đăng ký thành công
    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/')
    })
  })
  
  test('hiển thị lỗi khi đăng ký thất bại', async () => {
    // Mock signUp thất bại
    mockSignUp.mockResolvedValue({
      user: null,
      error: 'Email đã được sử dụng'
    })
    
    render(<RegisterPage />)
    
    // Nhập thông tin hợp lệ
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    fireEvent.change(screen.getByLabelText(/mật khẩu/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.change(screen.getByLabelText(/xác nhận mật khẩu/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.change(screen.getByLabelText(/tên đầy đủ/i), {
      target: { value: 'Test User' }
    })
    
    // Nhấn nút đăng ký
    fireEvent.click(screen.getByRole('button', { name: /đăng ký/i }))
    
    // Kiểm tra hiển thị thông báo lỗi
    await waitFor(() => {
      expect(screen.getByText(/email đã được sử dụng/i)).toBeInTheDocument()
    })
  })
})

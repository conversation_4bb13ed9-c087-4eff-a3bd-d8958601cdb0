# SoleStore - E-commerce Shoe Store

A modern, responsive e-commerce shoe store built with Next.js, TypeScript, Tailwind CSS, and shadcn/ui components. Features a complete shopping experience with product browsing, cart management, checkout flow, and user authentication.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm or pnpm

### Installation

1. Clone or download the project
2. Install dependencies:
```bash
npm install
# or
pnpm install
```

3. Start the development server:
```bash
npm run dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 📁 Project Structure

```
app/
├── (account)/          # Account-related pages
│   ├── auth/login/     # Login page
│   ├── auth/register/  # Registration page
│   ├── account/        # User dashboard
│   └── orders/         # Order history
├── (shop)/             # Shopping pages
│   ├── category/[slug]/ # Category listing
│   ├── product/[slug]/  # Product details
│   ├── cart/           # Shopping cart
│   ├── checkout/       # Checkout flow
│   └── search/         # Search results
├── layout.tsx          # Root layout
└── page.tsx           # Homepage

components/
├── ui/                # shadcn/ui components
├── product/           # Product-specific components
└── layout/            # Layout components

lib/
├── mock/              # Mock data
│   ├── products.ts    # Product data
│   ├── categories.ts  # Category data
│   └── orders.ts      # Order data
└── utils/             # Utility functions

store/
└── cart.ts           # Cart state management (Zustand)
```

## 🎨 Features

### Core Shopping Features
- **Product Catalog**: Browse products by category with filtering and sorting
- **Product Details**: Comprehensive product pages with galleries, reviews, and variants
- **Shopping Cart**: Persistent cart with quantity management
- **Checkout**: Multi-step checkout with form validation
- **Search**: Product search functionality
- **User Authentication**: Login/register forms (UI only)
- **Order Management**: Order history and tracking (mock)

### Design Features
- **Mobile-First**: Responsive design optimized for all devices
- **Accessibility**: WCAG AA compliant with proper ARIA labels and keyboard navigation
- **Performance**: Optimized images with Next.js Image component
- **Modern UI**: Clean, professional design with smooth animations
- **Dark Mode Ready**: CSS variables setup for easy theme switching

### Technical Features
- **Type Safety**: Full TypeScript implementation
- **State Management**: Zustand for cart state with localStorage persistence
- **Form Validation**: Client-side validation with proper error handling
- **SEO Optimized**: Meta tags, structured data, and semantic HTML
- **Production Ready**: Optimized build configuration

## 🛍️ User Flows

### Shopping Flow
1. **Browse Products**: Homepage → Category pages → Product details
2. **Add to Cart**: Select size/color → Add to cart → View cart
3. **Checkout**: Review cart → Fill shipping info → Payment → Confirmation

### Account Flow
1. **Registration**: Create account → Email verification (mock)
2. **Login**: Sign in → Dashboard → Order history
3. **Profile**: View/edit profile → Manage preferences

## 🔧 Customization

### Mock Data
Edit files in `lib/mock/` to customize:
- **Products**: Add/modify products in `products.ts`
- **Categories**: Update categories in `categories.ts`
- **Orders**: Customize order history in `orders.ts`

### Styling
- **Colors**: Modify CSS variables in `app/globals.css`
- **Components**: Customize shadcn/ui components in `components/ui/`
- **Layout**: Update header/footer in `components/layout/`

### Configuration
- **Site Info**: Update metadata in `app/layout.tsx`
- **Navigation**: Modify menu items in `components/layout/header.tsx`
- **Branding**: Change logo and brand name throughout components

## 🌐 Deployment

The app is configured for static export and can be deployed to any static hosting service:

```bash
npm run build
```

### Deployment Options
- **Vercel**: Automatic deployment with GitHub integration
- **Netlify**: Drag and drop build folder
- **GitHub Pages**: Static site hosting
- **Any CDN**: Upload `out/` folder contents

## 🧪 Demo Credentials

For testing authentication flows:
- **Email**: <EMAIL>
- **Password**: password123

## 📱 Browser Support

- Chrome/Edge 88+
- Firefox 85+
- Safari 14+
- Mobile browsers with ES2020 support

## 🔐 Security Notes

This is a frontend-only demo. In production:
- Implement real authentication with secure session management
- Add server-side validation for all forms
- Use HTTPS for all communications
- Implement proper payment processing with PCI compliance
- Add rate limiting and CSRF protection

## 🤝 Contributing

1. Follow the existing code style and component patterns
2. Maintain accessibility standards
3. Test responsive design on multiple devices
4. Update documentation for any new features

## 📄 License

This project is for educational and portfolio purposes. Feel free to use as inspiration for your own projects.
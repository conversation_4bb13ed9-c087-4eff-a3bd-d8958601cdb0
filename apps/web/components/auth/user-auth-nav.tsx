'use client'

/**
 * Component hiển thị thông tin người dùng và nút đăng xuất trong thanh điều hướng
 * Hiển thị các liên kết đăng nhập/đăng ký nếu chưa đăng nhập
 */
import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/lib/auth/auth-context'

export default function UserAuthNav() {
  const { user, signOut, isLoading } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  
  // Xử lý đăng xuất
  const handleSignOut = async () => {
    await signOut()
    setIsMenuOpen(false)
  }
  
  // Hiển thị trạng thái loading
  if (isLoading) {
    return (
      <div className="flex items-center space-x-2" data-testid="loading-skeleton">
        <div className="h-8 w-8 animate-pulse rounded-full bg-gray-200"></div>
        <div className="h-4 w-24 animate-pulse rounded bg-gray-200"></div>
      </div>
    )
  }
  
  // Hiển thị liên kết đăng nhập/đăng ký nếu chưa đăng nhập
  if (!user) {
    return (
      <div className="flex items-center space-x-4">
        <Link
          href="/login"
          className="rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100"
        >
          Đăng nhập
        </Link>
        <Link
          href="/register"
          className="rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700"
        >
          Đăng ký
        </Link>
      </div>
    )
  }
  
  // Hiển thị thông tin người dùng và nút đăng xuất nếu đã đăng nhập
  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center space-x-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
      >
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white">
          {user.full_name ? user.full_name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
        </div>
        <span className="hidden text-sm font-medium text-gray-700 md:block">
          {user.full_name || user.email}
        </span>
      </button>
      
      {/* Dropdown menu */}
      {isMenuOpen && (
        <div className="absolute right-0 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="px-4 py-2">
            <p className="text-sm font-medium text-gray-900">{user.full_name || 'Người dùng'}</p>
            <p className="truncate text-sm text-gray-500">{user.email}</p>
          </div>
          <hr className="my-1" />
          <Link
            href="/profile"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsMenuOpen(false)}
          >
            Hồ sơ của tôi
          </Link>
          <button
            className="block w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
            onClick={handleSignOut}
          >
            Đăng xuất
          </button>
        </div>
      )}
    </div>
  )
}

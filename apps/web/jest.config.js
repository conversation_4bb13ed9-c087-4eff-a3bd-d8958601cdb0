// jest.config.js
const nextJest = require('next/jest');

// Cung cấp đường dẫn đến ứng dụng Next.js để load next.config.js và .env files
const createJestConfig = nextJest({
  dir: './',
});

// Cấu hình Jest tùy chỉnh
/** @type {import('jest').Config} */
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    // X<PERSON> lý các alias trong tsconfig
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/'
  ],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!**/node_modules/**',
  ],
};

// createJestConfig được xuất ra để có thể sử dụng các cấu hình Next.js
module.exports = createJestConfig(customJestConfig);

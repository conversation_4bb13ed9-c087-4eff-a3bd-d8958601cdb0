// jest.setup.js
import '@testing-library/jest-dom';

// Thêm các thiết lập toàn cục cho Jest ở đây
// Ví dụ: mock các API toàn cục, thiết lập các biến môi trường, v.v.

// Mock cho matchMedia nếu cần
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // không dùng nữa trong trình duyệt hiện đại
    removeListener: jest.fn(), // không dùng nữa trong trình duyệt hiện đại
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

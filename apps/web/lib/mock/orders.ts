export interface OrderItem {
  productId: string;
  productName: string;
  productImage: string;
  size: string;
  color: string;
  quantity: number;
  price: number;
}

export interface Order {
  id: string;
  orderNumber: string;
  date: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  total: number;
  items: OrderItem[];
  shippingAddress: {
    fullName: string;
    address: string;
    city: string;
    postalCode: string;
    country: string;
  };
}

export const orders: Order[] = [
  {
    id: '1',
    orderNumber: 'ORD-001',
    date: '2024-01-15T10:30:00Z',
    status: 'delivered',
    total: 1599000,
    items: [
      {
        productId: '1',
        productName: 'Nike Air Max 270',
        productImage: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg',
        size: '40',
        color: 'Black',
        quantity: 1,
        price: 1599000,
      },
    ],
    shippingAddress: {
      fullName: '<PERSON>uy<PERSON><PERSON>',
      address: '123 Đường ABC',
      city: '<PERSON><PERSON>',
      postalCode: '70000',
      country: 'Vietnam',
    },
  },
  {
    id: '2',
    orderNumber: 'ORD-002',
    date: '2024-01-20T14:15:00Z',
    status: 'shipped',
    total: 7500000,
    items: [
      {
        productId: '5',
        productName: 'Jimmy Choo Romy 85',
        productImage: 'https://images.pexels.com/photos/336372/pexels-photo-336372.jpeg',
        size: '37',
        color: 'Black Patent',
        quantity: 1,
        price: 7500000,
      },
    ],
    shippingAddress: {
      fullName: 'Trần Thị B',
      address: '456 Đường XYZ',
      city: 'Hà Nội',
      postalCode: '10000',
      country: 'Vietnam',
    },
  },
];
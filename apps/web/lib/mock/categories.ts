export interface Category {
  id: string;
  slug: string;
  name: string;
  description: string;
  image: string;
  productCount: number;
}

export const categories: Category[] = [
  {
    id: '1',
    slug: 'sneakers',
    name: 'Sneakers',
    description: 'Comfortable and stylish sneakers for everyday wear and sports',
    image: 'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg',
    productCount: 4,
  },
  {
    id: '2',
    slug: 'heels',
    name: 'High Heels',
    description: 'Elegant high heels for special occasions and professional wear',
    image: 'https://images.pexels.com/photos/336372/pexels-photo-336372.jpeg',
    productCount: 2,
  },
  {
    id: '3',
    slug: 'boots',
    name: 'Boots',
    description: 'Durable and fashionable boots for all seasons',
    image: 'https://images.pexels.com/photos/267320/pexels-photo-267320.jpeg',
    productCount: 0,
  },
  {
    id: '4',
    slug: 'flats',
    name: 'Flats',
    description: 'Comfortable flat shoes perfect for daily activities',
    image: 'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg',
    productCount: 0,
  },
];

export const getCategoryBySlug = (slug: string): Category | undefined => {
  return categories.find(category => category.slug === slug);
};
export interface Product {
  id: string;
  slug: string;
  name: string;
  description: string;
  images: string[];
  price: number;
  discountPrice?: number;
  rating: number;
  reviewCount: number;
  sizes: string[];
  colors: { name: string; hex: string }[];
  category: string;
  brand: string;
  isNew?: boolean;
  isFeatured?: boolean;
}

export const products: Product[] = [
  {
    id: '1',
    slug: 'nike-air-max-270',
    name: 'Nike Air Max 270',
    description: 'The Nike Air Max 270 features Nike\'s biggest heel Air unit yet for a super-soft ride that feels as impossible as it looks.',
    images: [
      'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg',
      'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg',
    ],
    price: 1899000,
    discountPrice: 1599000,
    rating: 4.5,
    reviewCount: 128,
    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],
    colors: [
      { name: '<PERSON>', hex: '#000000' },
      { name: 'White', hex: '#FFFFFF' },
      { name: 'Red', hex: '#FF0000' },
    ],
    category: 'sneakers',
    brand: 'Nike',
    isFeatured: true,
    isNew: false,
  },
  {
    id: '2',
    slug: 'adidas-ultraboost-22',
    name: 'Adidas Ultraboost 22',
    description: 'Experience incredible energy return with every step in these running shoes built with responsive BOOST midsole cushioning.',
    images: [
      'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg',
      'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg',
    ],
    price: 2199000,
    rating: 4.7,
    reviewCount: 89,
    sizes: ['36', '37', '38', '39', '40', '41', '42', '43', '44'],
    colors: [
      { name: 'Core Black', hex: '#000000' },
      { name: 'Cloud White', hex: '#F8F8F8' },
      { name: 'Solar Red', hex: '#FF6B35' },
    ],
    category: 'sneakers',
    brand: 'Adidas',
    isFeatured: true,
    isNew: true,
  },
  {
    id: '3',
    slug: 'christian-louboutin-so-kate',
    name: 'Christian Louboutin So Kate 120',
    description: 'The So Kate 120mm pump is a timeless style that exemplifies Christian Louboutin\'s expertise in designing the perfect high heel.',
    images: [
      'https://images.pexels.com/photos/336372/pexels-photo-336372.jpeg',
      'https://images.pexels.com/photos/267320/pexels-photo-267320.jpeg',
    ],
    price: 15500000,
    rating: 4.9,
    reviewCount: 45,
    sizes: ['35', '36', '37', '38', '39', '40', '41'],
    colors: [
      { name: 'Black', hex: '#000000' },
      { name: 'Nude', hex: '#D4B5A0' },
      { name: 'Red', hex: '#8B0000' },
    ],
    category: 'heels',
    brand: 'Christian Louboutin',
    isFeatured: true,
    isNew: false,
  },
  {
    id: '4',
    slug: 'converse-chuck-taylor-all-star',
    name: 'Converse Chuck Taylor All Star',
    description: 'The original basketball shoe that started a cultural revolution. Classic canvas upper with rubber toe cap and vulcanized rubber sole.',
    images: [
      'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg',
      'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg',
    ],
    price: 899000,
    rating: 4.3,
    reviewCount: 234,
    sizes: ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44'],
    colors: [
      { name: 'Black', hex: '#000000' },
      { name: 'White', hex: '#FFFFFF' },
      { name: 'Red', hex: '#FF0000' },
      { name: 'Navy', hex: '#000080' },
    ],
    category: 'sneakers',
    brand: 'Converse',
    isFeatured: false,
    isNew: false,
  },
  {
    id: '5',
    slug: 'jimmy-choo-romy-85',
    name: 'Jimmy Choo Romy 85',
    description: 'A modern classic, the Romy 85 is an elegant pointed toe pump that will complement any outfit with its sleek silhouette.',
    images: [
      'https://images.pexels.com/photos/336372/pexels-photo-336372.jpeg',
      'https://images.pexels.com/photos/267320/pexels-photo-267320.jpeg',
    ],
    price: 8500000,
    discountPrice: 7500000,
    rating: 4.6,
    reviewCount: 67,
    sizes: ['35', '36', '37', '38', '39', '40', '41'],
    colors: [
      { name: 'Black Patent', hex: '#000000' },
      { name: 'Nude Patent', hex: '#D4B5A0' },
      { name: 'Navy Suede', hex: '#2F4F4F' },
    ],
    category: 'heels',
    brand: 'Jimmy Choo',
    isFeatured: false,
    isNew: true,
  },
  {
    id: '6',
    slug: 'vans-old-skool',
    name: 'Vans Old Skool',
    description: 'The original skate shoe with the iconic sidestripe. Durable canvas and suede uppers with signature waffle outsole.',
    images: [
      'https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg',
      'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg',
    ],
    price: 1299000,
    rating: 4.4,
    reviewCount: 189,
    sizes: ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44'],
    colors: [
      { name: 'Black/White', hex: '#000000' },
      { name: 'True White', hex: '#FFFFFF' },
      { name: 'Navy/White', hex: '#000080' },
    ],
    category: 'sneakers',
    brand: 'Vans',
    isFeatured: false,
    isNew: false,
  },
];

export const getProductBySlug = (slug: string): Product | undefined => {
  return products.find(product => product.slug === slug);
};

export const getProductsByCategory = (category: string): Product[] => {
  return products.filter(product => product.category === category);
};

export const getFeaturedProducts = (): Product[] => {
  return products.filter(product => product.isFeatured);
};

export const getNewProducts = (): Product[] => {
  return products.filter(product => product.isNew);
};

export const searchProducts = (query: string): Product[] => {
  const lowercaseQuery = query.toLowerCase();
  return products.filter(product => 
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.brand.toLowerCase().includes(lowercaseQuery) ||
    product.description.toLowerCase().includes(lowercaseQuery)
  );
};
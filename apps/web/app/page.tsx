import { ProductGrid } from '@/components/product/product-grid';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { categories } from '@/lib/mock/categories';
import { getFeaturedProducts, getNewProducts } from '@/lib/mock/products';
import { ArrowRight, CreditCard, RotateCcw, Shield, Truck } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

export default function HomePage() {
  const featuredProducts = getFeaturedProducts();
  const newProducts = getNewProducts();

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative min-h-[60vh] lg:min-h-[80vh] flex items-center justify-center bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900">
        <div className="absolute inset-0 bg-black/20" />
        <Image
          src="https://images.pexels.com/photos/2529148/pexels-photo-2529148.jpeg"
          alt="Hero background"
          fill
          className="object-cover mix-blend-overlay"
          priority
        />
        
        <div className="relative z-10 text-center text-white px-4 max-w-4xl">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Step Into
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
              Excellence
            </span>
          </h1>
          <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Discover our premium collection of footwear. From luxury heels to comfortable sneakers, find your perfect pair.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8" asChild>
              <Link href="/category/sneakers">
                Shop Sneakers
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 bg-white/10 border-white/20 text-white hover:bg-white hover:text-black" asChild>
              <Link href="/category/heels">
                Luxury Heels
              </Link>
            </Button>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4">
        {/* Categories Section */}
        <section className="py-12 lg:py-16">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Shop by Category</h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Explore our curated collection of premium footwear for every occasion and style preference.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link key={category.id} href={`/category/${category.slug}`}>
                <Card className="group cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02]">
                  <div className="aspect-square relative overflow-hidden">
                    <Image
                      src={category.image}
                      alt={category.name}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors duration-300" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <h3 className="text-xl md:text-2xl font-bold mb-2">{category.name}</h3>
                        <p className="text-sm opacity-90 mb-4 px-4">{category.description}</p>
                        <Button variant="secondary" size="sm" className="bg-white/90 text-black hover:bg-white">
                          Shop Now
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* Featured Products */}
        <section className="py-12 lg:py-16">
          <div className="flex flex-col md:flex-row md:items-end md:justify-between mb-8">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Products</h2>
              <p className="text-muted-foreground text-lg">Handpicked favorites loved by our customers</p>
            </div>
            <Button variant="outline" className="mt-4 md:mt-0" asChild>
              <Link href="/search">
                View All Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
          
          <ProductGrid products={featuredProducts} />
        </section>

        {/* New Arrivals */}
        {newProducts.length > 0 && (
          <section className="py-12 lg:py-16">
            <div className="flex flex-col md:flex-row md:items-end md:justify-between mb-8">
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-4">New Arrivals</h2>
                <p className="text-muted-foreground text-lg">Latest additions to our premium collection</p>
              </div>
              <Button variant="outline" className="mt-4 md:mt-0" asChild>
                <Link href="/category/sneakers">
                  Shop New
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            
            <ProductGrid products={newProducts} />
          </section>
        )}

        {/* Features Section */}
        <section className="py-12 lg:py-16 border-t">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: Truck,
                title: 'Free Shipping',
                description: 'Free shipping on orders over 2,000,000₫',
              },
              {
                icon: Shield,
                title: 'Authentic Guarantee',
                description: '100% authentic products guaranteed',
              },
              {
                icon: RotateCcw,
                title: '30-Day Returns',
                description: 'Easy returns within 30 days',
              },
              {
                icon: CreditCard,
                title: 'Secure Payment',
                description: 'Safe and secure payment processing',
              },
            ].map((feature, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-lg mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="font-semibold mb-2">{feature.title}</h3>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
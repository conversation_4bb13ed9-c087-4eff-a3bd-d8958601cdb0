'use client'

/**
 * <PERSON><PERSON> <PERSON>ồ sơ người dùng
 * Hi<PERSON><PERSON> thị và cho phép cập nhật thông tin cá nhân
 */
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth/auth-context'
import { createClient } from '@/lib/supabase/client'

export default function ProfilePage() {
  const { user, refreshUser } = useAuth()
  const router = useRouter()
  const supabase = createClient()
  
  const [fullName, setFullName] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState({ text: '', type: '' })
  
  // Chuyển hướng nếu chưa đăng nhập
  useEffect(() => {
    if (!user) {
      router.push('/login')
    } else {
      // Khởi tạo giá trị form từ thông tin người dùng
      setFullName(user.full_name || '')
    }
  }, [user, router])
  
  // <PERSON><PERSON> lý cập nhật hồ sơ
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) return
    
    setIsLoading(true)
    setMessage({ text: '', type: '' })
    
    try {
      // Cập nhật thông tin trong bảng profiles
      const { error } = await supabase
        .from('profiles')
        .update({ full_name: fullName })
        .eq('id', user.id)
      
      if (error) throw error
      
      // Làm mới thông tin người dùng
      await refreshUser()
      
      setMessage({
        text: 'Hồ sơ đã được cập nhật thành công!',
        type: 'success'
      })
    } catch (error: any) {
      setMessage({
        text: `Lỗi: ${error.message || 'Đã xảy ra lỗi khi cập nhật hồ sơ'}`,
        type: 'error'
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Hiển thị loading hoặc chuyển hướng nếu chưa đăng nhập
  if (!user) {
    return <div className="flex justify-center p-8">Đang chuyển hướng...</div>
  }
  
  return (
    <div className="container mx-auto max-w-md py-12">
      <h1 className="mb-8 text-center text-2xl font-bold">Hồ sơ của tôi</h1>
      
      {message.text && (
        <div
          className={`mb-4 rounded-md p-4 ${
            message.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
          }`}
        >
          {message.text}
        </div>
      )}
      
      <div className="rounded-lg border bg-card p-8 shadow-sm">
        <div className="mb-6">
          <p className="text-sm text-gray-500">Email</p>
          <p className="font-medium">{user.email}</p>
        </div>
        
        <form onSubmit={handleUpdateProfile}>
          <div className="mb-6">
            <label htmlFor="fullName" className="mb-2 block text-sm font-medium">
              Tên đầy đủ
            </label>
            <input
              id="fullName"
              type="text"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              placeholder="Nhập tên đầy đủ của bạn"
            />
          </div>
          
          <button
            type="submit"
            disabled={isLoading}
            className="w-full rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Đang cập nhật...' : 'Cập nhật hồ sơ'}
          </button>
        </form>
      </div>
    </div>
  )
}

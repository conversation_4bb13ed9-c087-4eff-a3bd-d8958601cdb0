import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { getProductBySlug, products } from '@/lib/mock/products';
import { ProductGallery } from './components/product-gallery';
import { ProductDetails } from './components/product-details';
import { ProductReviews } from './components/product-reviews';
import { RelatedProducts } from './components/related-products';

interface ProductPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const product = getProductBySlug(params.slug);
  
  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  return {
    title: `${product.name} - ${product.brand} - SoleStore`,
    description: product.description,
    openGraph: {
      title: product.name,
      description: product.description,
      images: product.images.map(image => ({ url: image })),
    },
  };
}

export async function generateStaticParams() {
  return products.map((product) => ({
    slug: product.slug,
  }));
}

export default function ProductPage({ params }: ProductPageProps) {
  const product = getProductBySlug(params.slug);
  
  if (!product) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Product Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-16">
        <ProductGallery images={product.images} productName={product.name} />
        <ProductDetails product={product} />
      </div>

      {/* Product Reviews */}
      <ProductReviews product={product} />

      {/* Related Products */}
      <RelatedProducts currentProduct={product} />
    </div>
  );
}
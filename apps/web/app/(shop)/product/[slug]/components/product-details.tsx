'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { StarRating } from '@/components/ui/star-rating';
import { Product } from '@/lib/mock/products';
import { calculateDiscount, formatCurrency } from '@/lib/utils/currency';
import { useCartStore } from '@/store/cart';
import { Heart, RotateCcw, Share2, Shield, ShoppingCart, Truck } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

interface ProductDetailsProps {
  product: Product;
}

export function ProductDetails({ product }: ProductDetailsProps) {
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedColor, setSelectedColor] = useState(product.colors[0]?.name || '');
  const [isWishlisted, setIsWishlisted] = useState(false);
  const addItem = useCartStore(state => state.addItem);

  const isOnSale = product.discountPrice && product.discountPrice < product.price;
  const discount = isOnSale ? calculateDiscount(product.price, product.discountPrice!) : 0;
  const finalPrice = product.discountPrice || product.price;

  const handleAddToCart = () => {
    if (!selectedSize) {
      toast.error('Please select a size');
      return;
    }

    const selectedColorObj = product.colors.find(color => color.name === selectedColor);
    
    addItem({
      productId: product.id,
      productName: product.name,
      productSlug: product.slug,
      productImage: product.images[0],
      price: product.price,
      discountPrice: product.discountPrice,
      size: selectedSize,
      color: selectedColorObj?.name || selectedColor,
    });

    toast.success('Added to cart!', {
      description: `${product.name} - Size ${selectedSize} - ${selectedColor}`,
    });
  };

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product.name,
        text: product.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
  };

  return (
    <div className="space-y-6">
      {/* Product Info Header */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <Badge variant="secondary" className="text-xs uppercase tracking-wide">
            {product.brand}
          </Badge>
          {product.isNew && (
            <Badge variant="default" className="bg-blue-600 hover:bg-blue-700">
              New
            </Badge>
          )}
          {isOnSale && (
            <Badge variant="destructive">
              -{discount}% Off
            </Badge>
          )}
        </div>
        
        <h1 className="text-2xl md:text-3xl font-bold mb-2">{product.name}</h1>
        
        <div className="flex items-center gap-2 mb-4">
          <StarRating rating={product.rating} size="md" showNumber />
          <span className="text-sm text-muted-foreground">
            ({product.reviewCount} reviews)
          </span>
        </div>

        {/* Price */}
        <div className="flex items-baseline gap-3 mb-6">
          <span className="text-2xl md:text-3xl font-bold">
            {formatCurrency(finalPrice)}
          </span>
          {isOnSale && (
            <span className="text-lg text-muted-foreground line-through">
              {formatCurrency(product.price)}
            </span>
          )}
        </div>
      </div>

      <Separator />

      {/* Color Selection */}
      {product.colors.length > 0 && (
        <div>
          <Label className="text-base font-medium mb-3 block">
            Color: {selectedColor}
          </Label>
          <div className="flex gap-2 flex-wrap">
            {product.colors.map((color) => (
              <button
                key={color.name}
                onClick={() => setSelectedColor(color.name)}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  selectedColor === color.name 
                    ? 'border-primary ring-2 ring-primary/30 scale-110' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                style={{ backgroundColor: color.hex }}
                aria-label={`Select ${color.name} color`}
                title={color.name}
              />
            ))}
          </div>
        </div>
      )}

      {/* Size Selection */}
      {product.sizes.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-3">
            <Label className="text-base font-medium">Size</Label>
            <Button variant="link" className="text-sm h-auto p-0">
              Size Guide
            </Button>
          </div>
          <RadioGroup value={selectedSize} onValueChange={setSelectedSize}>
            <div className="grid grid-cols-4 gap-2">
              {product.sizes.map((size) => (
                <div key={size} className="relative">
                  <RadioGroupItem
                    value={size}
                    id={`size-${size}`}
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={`size-${size}`}
                    className="flex items-center justify-center rounded-md border-2 border-muted bg-popover p-3 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary cursor-pointer transition-colors"
                  >
                    {size}
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        </div>
      )}

      {/* Add to Cart & Wishlist */}
      <div className="flex gap-3">
        <Button 
          onClick={handleAddToCart}
          className="flex-1"
          size="lg"
          disabled={product.sizes.length > 0 && !selectedSize}
        >
          <ShoppingCart className="h-5 w-5 mr-2" />
          Add to Cart
        </Button>
        <Button
          onClick={handleWishlist}
          variant={isWishlisted ? "default" : "outline"}
          size="lg"
          className="px-4"
          aria-label={isWishlisted ? "Remove from wishlist" : "Add to wishlist"}
        >
          <Heart className={`h-5 w-5 ${isWishlisted ? 'fill-current' : ''}`} />
        </Button>
        <Button
          onClick={handleShare}
          variant="outline"
          size="lg"
          className="px-4"
          aria-label="Share product"
        >
          <Share2 className="h-5 w-5" />
        </Button>
      </div>

      <Separator />

      {/* Product Description */}
      <div>
        <h3 className="text-lg font-medium mb-3">Description</h3>
        <p className="text-muted-foreground leading-relaxed">{product.description}</p>
      </div>

      <Separator />

      {/* Features */}
      <div className="space-y-3">
        <div className="flex items-center gap-3">
          <Truck className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm">Free shipping on orders over 2,000,000₫</span>
        </div>
        <div className="flex items-center gap-3">
          <Shield className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm">100% authentic guarantee</span>
        </div>
        <div className="flex items-center gap-3">
          <RotateCcw className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm">30-day return policy</span>
        </div>
      </div>
    </div>
  );
}
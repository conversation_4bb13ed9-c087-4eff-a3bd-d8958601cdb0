import { ProductGrid } from '@/components/product/product-grid';
import { Product, getProductsByCategory } from '@/lib/mock/products';

interface RelatedProductsProps {
  currentProduct: Product;
}

export function RelatedProducts({ currentProduct }: RelatedProductsProps) {
  const relatedProducts = getProductsByCategory(currentProduct.category)
    .filter(product => product.id !== currentProduct.id)
    .slice(0, 4);

  if (relatedProducts.length === 0) {
    return null;
  }

  return (
    <section className="py-8">
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-2">You Might Also Like</h2>
        <p className="text-muted-foreground">Similar products in this category</p>
      </div>
      
      <ProductGrid products={relatedProducts} />
    </section>
  );
}
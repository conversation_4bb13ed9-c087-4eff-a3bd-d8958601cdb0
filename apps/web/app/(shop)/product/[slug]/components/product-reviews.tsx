'use client';

import { useState } from 'react';
import { Star, ThumbsUp, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StarRating } from '@/components/ui/star-rating';
import { Product } from '@/lib/mock/products';

interface ProductReviewsProps {
  product: Product;
}

// Mock reviews data
const reviews = [
  {
    id: '1',
    userName: 'Nguyễn Văn A',
    userAvatar: '',
    rating: 5,
    date: '2024-01-15',
    title: 'Excellent quality!',
    content: 'Amazing shoes, very comfortable and exactly as described. The quality is outstanding and delivery was fast.',
    images: [],
    helpful: 12,
    verified: true,
  },
  {
    id: '2',
    userName: '<PERSON><PERSON><PERSON><PERSON>',
    userAvatar: '',
    rating: 4,
    date: '2024-01-10',
    title: 'Good fit and style',
    content: 'Really like the design and the fit is perfect. Only minor issue is that they run slightly small.',
    images: [],
    helpful: 8,
    verified: true,
  },
  {
    id: '3',
    userName: 'Lê Minh C',
    userAvatar: '',
    rating: 5,
    date: '2024-01-05',
    title: 'Highly recommend',
    content: 'Perfect for both casual and formal occasions. The quality justifies the price.',
    images: [],
    helpful: 15,
    verified: true,
  },
];

export function ProductReviews({ product }: ProductReviewsProps) {
  const [sortBy, setSortBy] = useState('newest');

  // Mock rating distribution
  const ratingDistribution = [
    { rating: 5, count: 78, percentage: 65 },
    { rating: 4, count: 30, percentage: 25 },
    { rating: 3, count: 8, percentage: 7 },
    { rating: 2, count: 2, percentage: 2 },
    { rating: 1, count: 1, percentage: 1 },
  ];

  const averageRating = 4.5;
  const totalReviews = product.reviewCount;

  return (
    <div className="py-8">
      <Tabs defaultValue="reviews" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="reviews">Reviews ({totalReviews})</TabsTrigger>
          <TabsTrigger value="description">Description</TabsTrigger>
        </TabsList>
        
        <TabsContent value="description" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Product Details</h3>
              <div className="prose max-w-none text-muted-foreground">
                <p>{product.description}</p>
                <p className="mt-4">
                  This premium footwear combines style, comfort, and durability to deliver an exceptional wearing experience. 
                  Crafted with attention to detail and using high-quality materials, these shoes are designed to meet the 
                  highest standards of both fashion and functionality.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Rating Summary */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-medium">Customer Reviews</h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-4xl font-bold mb-2">{averageRating}</div>
                    <StarRating rating={averageRating} size="lg" />
                    <p className="text-sm text-muted-foreground mt-2">
                      Based on {totalReviews} reviews
                    </p>
                  </div>

                  <div className="space-y-2">
                    {ratingDistribution.map((item) => (
                      <div key={item.rating} className="flex items-center gap-2 text-sm">
                        <span className="w-8">{item.rating}★</span>
                        <Progress value={item.percentage} className="flex-1 h-2" />
                        <span className="w-8 text-right">{item.count}</span>
                      </div>
                    ))}
                  </div>

                  <Button className="w-full" variant="outline">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Write a Review
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Reviews List */}
            <div className="lg:col-span-2">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium">Reviews</h3>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="text-sm border rounded px-3 py-1"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="highest">Highest Rating</option>
                  <option value="lowest">Lowest Rating</option>
                  <option value="helpful">Most Helpful</option>
                </select>
              </div>

              <div className="space-y-6">
                {reviews.map((review) => (
                  <Card key={review.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <Avatar>
                          <AvatarImage src={review.userAvatar} />
                          <AvatarFallback>
                            {review.userName.split(' ').map(n => n[0]).join('').slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{review.userName}</h4>
                                {review.verified && (
                                  <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">
                                    Verified Purchase
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center gap-2 mt-1">
                                <StarRating rating={review.rating} size="sm" />
                                <span className="text-sm text-muted-foreground">
                                  {new Date(review.date).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div>
                            <h5 className="font-medium mb-2">{review.title}</h5>
                            <p className="text-muted-foreground">{review.content}</p>
                          </div>

                          <div className="flex items-center gap-4 text-sm">
                            <Button variant="ghost" size="sm" className="h-8">
                              <ThumbsUp className="h-3 w-3 mr-1" />
                              Helpful ({review.helpful})
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="mt-8 text-center">
                <Button variant="outline">Load More Reviews</Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
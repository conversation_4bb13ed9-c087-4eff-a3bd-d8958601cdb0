'use client';

import { useSearchParams } from 'next/navigation';
import { CheckCircle, Package, Truck, CreditCard, ArrowRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import Link from 'next/link';

export default function OrderConfirmationPage() {
  const searchParams = useSearchParams();
  const orderNumber = searchParams.get('orderNumber') || 'ORD-123456';

  // Mock order details
  const orderDetails = {
    orderNumber,
    email: '<EMAIL>',
    estimatedDelivery: '3-5 business days',
    total: '1,759,000₫',
    items: [
      {
        name: 'Nike Air Max 270',
        size: '40',
        color: 'Black',
        quantity: 1,
        price: '1,599,000₫',
      },
    ],
    shippingAddress: {
      name: '<PERSON>',
      address: '123 Main Street',
      city: 'Ho Chi Minh City, 70000',
      country: 'Vietnam',
    },
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold mb-2">Order Confirmed!</h1>
          <p className="text-muted-foreground">
            Thank you for your purchase. Your order has been placed successfully.
          </p>
        </div>

        {/* Order Summary */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Order Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Order Number</h3>
                <p className="font-mono">{orderDetails.orderNumber}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground">Email</h3>
                <p>{orderDetails.email}</p>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-medium mb-3">Items Ordered</h3>
              <div className="space-y-3">
                {orderDetails.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Size: {item.size}, Color: {item.color}
                      </p>
                      <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                    </div>
                    <p className="font-medium">{item.price}</p>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            <div className="flex justify-between font-semibold text-lg">
              <span>Total</span>
              <span>{orderDetails.total}</span>
            </div>
          </CardContent>
        </Card>

        {/* Shipping Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Shipping Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Shipping Address</h3>
                <div className="text-sm">
                  <p className="font-medium">{orderDetails.shippingAddress.name}</p>
                  <p>{orderDetails.shippingAddress.address}</p>
                  <p>{orderDetails.shippingAddress.city}</p>
                  <p>{orderDetails.shippingAddress.country}</p>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Estimated Delivery</h3>
                <p className="text-sm">{orderDetails.estimatedDelivery}</p>
                <p className="text-sm text-muted-foreground mt-2">
                  You'll receive tracking information via email once your order ships.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Information */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Cash on Delivery</p>
                <p className="text-sm text-muted-foreground">
                  Pay when you receive your order
                </p>
              </div>
              <div className="text-right">
                <p className="font-semibold">{orderDetails.total}</p>
                <p className="text-sm text-green-600">Pending Payment</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <Button asChild className="flex-1">
            <Link href="/orders">
              View Order History
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
          <Button variant="outline" asChild className="flex-1">
            <Link href="/">Continue Shopping</Link>
          </Button>
        </div>

        {/* Additional Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
          <h3 className="font-medium text-blue-900 mb-2">What happens next?</h3>
          <ul className="space-y-1 text-blue-800">
            <li>• You'll receive an order confirmation email shortly</li>
            <li>• We'll send you tracking information once your order ships</li>
            <li>• Your order will be delivered within {orderDetails.estimatedDelivery}</li>
            <li>• If you have questions, contact <NAME_EMAIL></li>
          </ul>
        </div>
      </div>
    </div>
  );
}
'use client';

import { useSearchParams } from 'next/navigation';
import { Search } from 'lucide-react';
import { ProductGrid } from '@/components/product/product-grid';
import { searchProducts } from '@/lib/mock/products';

export function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const results = query ? searchProducts(query) : [];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Search Results</h1>
        {query ? (
          <p className="text-muted-foreground">
            {results.length} result{results.length !== 1 ? 's' : ''} found for "{query}"
          </p>
        ) : (
          <p className="text-muted-foreground">Enter a search term to find products</p>
        )}
      </div>

      {query && results.length === 0 && (
        <div className="text-center py-16">
          <Search className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-xl font-semibold mb-2">No results found</h2>
          <p className="text-muted-foreground mb-4">
            Sorry, we couldn't find any products matching "{query}".
          </p>
          <div className="text-sm text-muted-foreground">
            Try:
            <ul className="mt-2 space-y-1">
              <li>• Checking your spelling</li>
              <li>• Using different keywords</li>
              <li>• Searching for a specific brand or category</li>
            </ul>
          </div>
        </div>
      )}

      {results.length > 0 && <ProductGrid products={results} />}
    </div>
  );
}
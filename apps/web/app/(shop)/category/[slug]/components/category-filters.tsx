'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CategoryFiltersProps {
  categorySlug: string;
}

export function CategoryFilters({ categorySlug }: CategoryFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const brands = [
    { id: 'nike', name: 'Nike', count: 1 },
    { id: 'adidas', name: 'Adidas', count: 1 },
    { id: 'converse', name: 'Converse', count: 1 },
    { id: 'vans', name: 'Vans', count: 1 },
  ];

  const sizes = ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44'];

  const priceRanges = [
    { id: '0-1000000', name: 'Under 1,000,000₫', min: 0, max: 1000000 },
    { id: '1000000-2000000', name: '1,000,000₫ - 2,000,000₫', min: 1000000, max: 2000000 },
    { id: '2000000-5000000', name: '2,000,000₫ - 5,000,000₫', min: 2000000, max: 5000000 },
    { id: '5000000-10000000', name: '5,000,000₫ - 10,000,000₫', min: 5000000, max: 10000000 },
    { id: '10000000+', name: 'Over 10,000,000₫', min: 10000000, max: Infinity },
  ];

  const handleFilterChange = (filterType: string, value: string, checked: boolean) => {
    const params = new URLSearchParams(searchParams.toString());
    const currentValues = params.get(filterType)?.split(',') || [];
    
    if (checked) {
      if (!currentValues.includes(value)) {
        currentValues.push(value);
      }
    } else {
      const index = currentValues.indexOf(value);
      if (index > -1) {
        currentValues.splice(index, 1);
      }
    }

    if (currentValues.length > 0) {
      params.set(filterType, currentValues.join(','));
    } else {
      params.delete(filterType);
    }

    router.push(`/category/${categorySlug}?${params.toString()}`);
  };

  const clearAllFilters = () => {
    router.push(`/category/${categorySlug}`);
  };

  const currentBrands = searchParams.get('brand')?.split(',') || [];
  const currentSizes = searchParams.get('size')?.split(',') || [];
  const currentPrices = searchParams.get('price')?.split(',') || [];

  const hasFilters = currentBrands.length > 0 || currentSizes.length > 0 || currentPrices.length > 0;

  return (
    <div className="space-y-6">
      {/* Clear Filters */}
      {hasFilters && (
        <div className="flex justify-between items-center">
          <h3 className="font-medium">Filters</h3>
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            Clear All
          </Button>
        </div>
      )}

      {/* Brand Filter */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Brand</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {brands.map((brand) => (
            <div key={brand.id} className="flex items-center space-x-2">
              <Checkbox
                id={`brand-${brand.id}`}
                checked={currentBrands.includes(brand.id)}
                onCheckedChange={(checked) => 
                  handleFilterChange('brand', brand.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`brand-${brand.id}`} 
                className="flex-1 text-sm cursor-pointer"
              >
                {brand.name} ({brand.count})
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Size Filter */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Size</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-2">
            {sizes.map((size) => (
              <div key={size} className="flex items-center space-x-2">
                <Checkbox
                  id={`size-${size}`}
                  checked={currentSizes.includes(size)}
                  onCheckedChange={(checked) => 
                    handleFilterChange('size', size, checked as boolean)
                  }
                />
                <Label 
                  htmlFor={`size-${size}`} 
                  className="text-sm cursor-pointer"
                >
                  {size}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Price Filter */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Price Range</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {priceRanges.map((range) => (
            <div key={range.id} className="flex items-center space-x-2">
              <Checkbox
                id={`price-${range.id}`}
                checked={currentPrices.includes(range.id)}
                onCheckedChange={(checked) => 
                  handleFilterChange('price', range.id, checked as boolean)
                }
              />
              <Label 
                htmlFor={`price-${range.id}`} 
                className="flex-1 text-sm cursor-pointer"
              >
                {range.name}
              </Label>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
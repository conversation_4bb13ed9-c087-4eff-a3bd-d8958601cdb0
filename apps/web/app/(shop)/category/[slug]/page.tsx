import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProductGrid } from '@/components/product/product-grid';
import { getCategoryBySlug, categories } from '@/lib/mock/categories';
import { getProductsByCategory } from '@/lib/mock/products';
import { CategoryFilters } from './components/category-filters';
import { CategorySort } from './components/category-sort';

interface CategoryPageProps {
  params: {
    slug: string;
  };
  searchParams: {
    sort?: string;
    price?: string;
    brand?: string;
    size?: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const category = getCategoryBySlug(params.slug);
  
  if (!category) {
    return {
      title: 'Category Not Found',
    };
  }

  return {
    title: `${category.name} - SoleStore`,
    description: category.description,
  };
}

export async function generateStaticParams() {
  return categories.map((category) => ({
    slug: category.slug,
  }));
}

export default function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const category = getCategoryBySlug(params.slug);
  
  if (!category) {
    notFound();
  }

  let products = getProductsByCategory(params.slug);

  // Apply sorting
  if (searchParams.sort) {
    switch (searchParams.sort) {
      case 'price-asc':
        products = products.sort((a, b) => {
          const priceA = a.discountPrice || a.price;
          const priceB = b.discountPrice || b.price;
          return priceA - priceB;
        });
        break;
      case 'price-desc':
        products = products.sort((a, b) => {
          const priceA = a.discountPrice || a.price;
          const priceB = b.discountPrice || b.price;
          return priceB - priceA;
        });
        break;
      case 'newest':
        products = products.sort((a, b) => {
          if (a.isNew && !b.isNew) return -1;
          if (!a.isNew && b.isNew) return 1;
          return 0;
        });
        break;
      case 'rating':
        products = products.sort((a, b) => b.rating - a.rating);
        break;
    }
  }

  // Apply filters (simplified for demo)
  if (searchParams.brand) {
    const brands = searchParams.brand.split(',');
    products = products.filter(product => 
      brands.includes(product.brand.toLowerCase().replace(/\s+/g, '-'))
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-2">{category.name}</h1>
        <p className="text-muted-foreground text-lg mb-4">{category.description}</p>
        <p className="text-sm text-muted-foreground">
          {products.length} product{products.length !== 1 ? 's' : ''} found
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Desktop Sidebar Filters */}
        <aside className="hidden lg:block w-64 flex-shrink-0">
          <CategoryFilters categorySlug={params.slug} />
        </aside>

        {/* Main Content */}
        <div className="flex-1">
          {/* Mobile Filters & Sort */}
          <div className="flex items-center justify-between mb-6 lg:justify-end">
            <div className="lg:hidden">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
            
            <CategorySort />
          </div>

          {/* Products Grid */}
          <ProductGrid products={products} />

          {/* No products message */}
          {products.length === 0 && (
            <div className="text-center py-16">
              <h3 className="text-lg font-medium mb-2">No products found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your filters or browse our other categories.
              </p>
              <Button asChild>
                <a href="/">Browse All Products</a>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
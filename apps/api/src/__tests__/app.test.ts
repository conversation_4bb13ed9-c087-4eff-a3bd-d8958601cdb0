import request from 'supertest';
import express from 'express';

// Tạo một ứng dụng Express đơn giản để test
const app = express();
app.get('/test', (req, res) => {
  res.status(200).json({ message: 'Test API thành công' });
});

describe('API Tests', () => {
  it('GET /test - nên trả về thông báo thành công', async () => {
    // Thực hiện request
    const response = await request(app).get('/test');
    
    // <PERSON><PERSON><PERSON> tra kết quả
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('message');
    expect(response.body.message).toBe('Test API thành công');
  });
});

import express, { Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';

// Đọc biến môi trường từ file .env
dotenv.config();

// Khởi tạo Express app
const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(helmet()); // Bảo mật HTTP headers
app.use(cors()); // Cho phép CORS
app.use(express.json()); // Parse JSON request body

// Route cơ bản
app.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'API đang hoạt động!',
    version: '0.1.0',
  });
});

// API routes
app.use('/api/health', (req: Request, res: Response) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Xử lý lỗi 404
app.use((req: Request, res: Response) => {
  res.status(404).json({ error: 'Không tìm thấy route' });
});

// Khởi động server
app.listen(port, () => {
  console.log(`Server đang chạy tại http://localhost:${port}`);
});
